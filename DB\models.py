from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Date, Enum, DateTime, Text, Index
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


from sqlalchemy import Column, Integer, String, Date, DateTime, Text

class WhatsAppMessage(Base):
    __tablename__ = 'whatsapp_messages'

    id = Column(Integer, primary_key=True, autoincrement=True)
    wa_id = Column(String(20), nullable=False)
    message_id = Column(String(100), nullable=False)
    report_type = Column(String(50), nullable=False)
    plant_short_name = Column(String(100), nullable=False)
    plant_long_name = Column(String(100), nullable=False)
    date = Column(Date, nullable=False)
    dgr_path = Column(String(500))  # <-- New column

    def to_dict(self):
        return {
            'id': self.id,
            'wa_id': self.wa_id,
            'message_id': self.message_id,
            'report_type': self.report_type,
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'date': self.date,
            'dgr_path': self.dgr_path
        }





class DgrBothDb(Base):
    __tablename__ = 'dgr_both_db'
    saved_count = Column(Integer, default=0)

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name_solar = Column(String, nullable=False)
    plant_long_name_solar = Column(String, nullable=False)
    generation_solar = Column(Float, nullable=False)
    pr = Column(Float, nullable=False)
    poa = Column(Float, nullable=False)
    generation_solar_monthly = Column(Float)
    pr_monthly = Column(Float)
    poa_monthly = Column(Float)

    plant_short_name_wind = Column(String, nullable=False)
    plant_long_name_wind = Column(String, nullable=False)
    generation_wind = Column(Float, nullable=False)
    wind_speed = Column(Float, nullable=False)
    generation_wind_monthly = Column(Float)
    wind_speed_monthly = Column(Float)

    # ✅ New edit fields
    edit_generation_solar = Column(Float)
    edit_pr = Column(Float)
    edit_poa = Column(Float)
    edit_generation_solar_monthly = Column(Float)
    edit_pr_monthly = Column(Float)
    edit_poa_monthly = Column(Float)

    edit_generation_wind = Column(Float)
    edit_wind_speed = Column(Float)
    edit_generation_wind_monthly = Column(Float)
    edit_wind_speed_monthly = Column(Float)

    # New columns for CSV report data
    csv_report_data = Column(Text)
    edit_csv_report_data = Column(Text)

    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', 'Sent Updated', 'Saved', 'Updated', "Don't update", name='status_enum'), default='Pending')
    regenerate = Column(Boolean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(Boolean, default=False)
    edit_action = Column(Boolean, default=False)
    save_action = Column(Boolean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name_solar': self.plant_short_name_solar,
            'plant_long_name_solar': self.plant_long_name_solar,
            'generation_solar': self.generation_solar,
            'pr': self.pr,
            'poa': self.poa,
            'generation_solar_monthly': self.generation_solar_monthly,
            'pr_monthly': self.pr_monthly,
            'poa_monthly': self.poa_monthly,
            'plant_short_name_wind': self.plant_short_name_wind,
            'plant_long_name_wind': self.plant_long_name_wind,
            'generation_wind': self.generation_wind,
            'wind_speed': self.wind_speed,
            'generation_wind_monthly': self.generation_wind_monthly,
            'wind_speed_monthly': self.wind_speed_monthly,
            'edit_generation_solar': self.edit_generation_solar,
            'edit_pr': self.edit_pr,
            'edit_poa': self.edit_poa,
            'edit_generation_solar_monthly': self.edit_generation_solar_monthly,
            'edit_pr_monthly': self.edit_pr_monthly,
            'edit_poa_monthly': self.edit_poa_monthly,
            'edit_generation_wind': self.edit_generation_wind,
            'edit_wind_speed': self.edit_wind_speed,
            'edit_generation_wind_monthly': self.edit_generation_wind_monthly,
            'edit_wind_speed_monthly': self.edit_wind_speed_monthly,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'edit_action': self.edit_action,
            'dont_send': self.dont_send,
            'save_action': self.save_action,
            'csv_report_data': self.csv_report_data,
            'edit_csv_report_data': self.edit_csv_report_data,
            'saved_count': self.saved_count
        }



class SolarReport(Base):
    __tablename__ = 'dgr_solar_db'
    saved_count = Column(Integer, default=0)

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name = Column(String, nullable=False)
    plant_long_name = Column(String, nullable=False)
    generation = Column(Float, nullable=False)
    pr = Column(Float, nullable=False)
    poa = Column(Float, nullable=False)
    generation_monthly = Column(Float)
    pr_monthly = Column(Float)
    poa_monthly = Column(Float)

    # ✅ New edit fields
    edit_generation = Column(Float)
    edit_pr = Column(Float)
    edit_poa = Column(Float)
    edit_generation_monthly = Column(Float)
    edit_pr_monthly = Column(Float)
    edit_poa_monthly = Column(Float)

    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', 'Sent Updated', 'Saved', 'Updated', "Don't update", name='status_enum'), default='Pending')
    regenerate = Column(Boolean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(Boolean, default=False)
    edit_action = Column(Boolean, default=False)
    save_action = Column(Boolean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'generation': self.generation,
            'pr': self.pr,
            'poa': self.poa,
            'generation_monthly': self.generation_monthly,
            'pr_monthly': self.pr_monthly,
            'poa_monthly': self.poa_monthly,
            'edit_generation': self.edit_generation,
            'edit_pr': self.edit_pr,
            'edit_poa': self.edit_poa,
            'edit_generation_monthly': self.edit_generation_monthly,
            'edit_pr_monthly': self.edit_pr_monthly,
            'edit_poa_monthly': self.edit_poa_monthly,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'edit_action': self.edit_action,
            'dont_send': self.dont_send,
            'save_action': self.save_action,
            'saved_count': self.saved_count
        }
    


class WindReport(Base):
    __tablename__ = 'dgr_wind_db'
    saved_count = Column(Integer, default=0)

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name = Column(String, nullable=False)
    plant_long_name = Column(String, nullable=False)
    generation = Column(Float, nullable=False)
    wind_speed = Column(Float, nullable=False)
    generation_monthly = Column(Float)
    wind_speed_monthly = Column(Float)

    # ✅ New edit fields
    edit_generation = Column(Float)
    edit_wind_speed = Column(Float)
    edit_generation_monthly = Column(Float)
    edit_wind_speed_monthly = Column(Float)

    # New column for CSV report data (as JSON string or plain text)
    csv_report_data = Column(Text)
    # New column for edited CSV report data
    edit_csv_report_data = Column(Text)

    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', 'Sent Updated', 'Saved', 'Updated', "Don't update", name='status_enum'), default='Pending')
    regenerate = Column(Boolean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(Boolean, default=False)
    edit_action = Column(Boolean, default=False)
    save_action = Column(Boolean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'generation': self.generation,
            'wind_speed': self.wind_speed,
            'generation_monthly': self.generation_monthly,
            'wind_speed_monthly': self.wind_speed_monthly,
            'edit_generation': self.edit_generation,
            'edit_wind_speed': self.edit_wind_speed,
            'edit_generation_monthly': self.edit_generation_monthly,
            'edit_wind_speed_monthly': self.edit_wind_speed_monthly,
            'csv_report_data': self.csv_report_data,
            'edit_csv_report_data': self.edit_csv_report_data,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'edit_action': self.edit_action,
            'dont_send': self.dont_send,
            'save_action': self.save_action,
            'saved_count': self.saved_count
        }


class ReportStatus(Base):
    __tablename__ = 'report_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    message_id = Column(String(255), unique=True)
    recipient_id = Column(String(255))
    status = Column(String(50))
    status_updated_at = Column(DateTime)
    send_date = Column(DateTime)
    report_date = Column(Date)  # New column

    plant_id = Column(String(100))
    client_name = Column(String(255))
    type = Column(String(50))
    combined = Column(String(100))
    contact_person = Column(Text)

# ----------------------
# ChatHistory model for MCP client chat memory
# ----------------------
class ChatHistory(Base):
    __tablename__ = 'chat_history'

    id = Column(Integer, primary_key=True, autoincrement=True)
    thread_id = Column(String(100), nullable=False, index=True)
    role = Column(String(20), nullable=False)
    content = Column(Text, nullable=False)
    timestamp = Column(String(40), nullable=False)  # ISO string for compatibility

    def to_dict(self):
        return {
            "id": self.id,
            "thread_id": self.thread_id,
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp
        }

    __table_args__ = (
        Index('ix_chat_history_thread_id', 'thread_id'),
    )

# ----------------------
# User model for multi-user capability (initial table only)
# ----------------------
from datetime import datetime

class AuditLog(Base):
    __tablename__ = 'audit_logs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True, index=True)
    username = Column(String(50), nullable=True)
    action_type = Column(String(50), nullable=False)
    target_type = Column(String(50), nullable=True)
    target_id = Column(Integer, nullable=True)
    details = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "username": self.username,
            "action_type": self.action_type,
            "target_type": self.target_type,
            "target_id": self.target_id,
            "details": self.details,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "ip_address": self.ip_address
        }

# ----------------------
# New Models for Solar Inverter Data and Wind Turbine Data
# ----------------------

class SolarInverterData(Base):
    __tablename__ = 'solar_inverter_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, nullable=False)
    plant_id = Column(String(50), nullable=False)
    plant_name = Column(String(255))
    inverter_name = Column(String(100))
    generation = Column(Float(precision=2))
    pr = Column(Float(precision=2))
    poa = Column(Float(precision=2))
    generation_monthly = Column(Float(precision=2))
    pr_monthly = Column(Float(precision=2))
    poa_monthly = Column(Float(precision=2))
    edit_action = Column(String(100))
    reason_edit = Column(Text)
    edit_generation = Column(Float(precision=2))
    edit_pr = Column(Float(precision=2))
    edit_poa = Column(Float(precision=2))
    edit_generation_monthly = Column(Float(precision=2))
    edit_pr_monthly = Column(Float(precision=2))
    edit_poa_monthly = Column(Float(precision=2))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    def to_dict(self):
        return {
            "id": self.id,
            "date": self.date.isoformat() if self.date else None,
            "plant_id": self.plant_id,
            "plant_name": self.plant_name,
            "inverter_name": self.inverter_name,
            "generation": self.generation,
            "pr": self.pr,
            "poa": self.poa,
            "generation_monthly": self.generation_monthly,
            "pr_monthly": self.pr_monthly,
            "poa_monthly": self.poa_monthly,
            "edit_action": self.edit_action,
            "reason_edit": self.reason_edit,
            "edit_generation": self.edit_generation,
            "edit_pr": self.edit_pr,
            "edit_poa": self.edit_poa,
            "edit_generation_monthly": self.edit_generation_monthly,
            "edit_pr_monthly": self.edit_pr_monthly,
            "edit_poa_monthly": self.edit_poa_monthly,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
class PlantAlarm(Base):
    __tablename__ = 'plant_alarms'

    id = Column(Integer, primary_key=True, autoincrement=True)
    alarm_date = Column(Date, nullable=False)
    plant_name = Column(String(255), nullable=False)
    plant_id = Column(String(100), nullable=False)
    alarm_name = Column(String(255), nullable=False)
    controller_name = Column(String(255), nullable=True)
    message = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    raised_time = Column(DateTime, nullable=False)
    resolved_time = Column(DateTime, nullable=True)
    duration_minutes = Column(Integer, nullable=True)

    __table_args__ = (
        Index('idx_plant_id', 'plant_id'),
        Index('idx_alarm_date', 'alarm_date'),
        Index('idx_severity', 'severity'),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "alarm_date": self.alarm_date.isoformat() if self.alarm_date else None,
            "plant_name": self.plant_name,
            "plant_id": self.plant_id,
            "alarm_name": self.alarm_name,
            "controller_name": self.controller_name,
            "message": self.message,
            "severity": self.severity,
            "state": self.state,
            "raised_time": self.raised_time.isoformat() if self.raised_time else None,
            "resolved_time": self.resolved_time.isoformat() if self.resolved_time else None,
            "duration_minutes": self.duration_minutes
        }

class WindTurbineData(Base):
    __tablename__ = 'wind_turbine_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, nullable=False)
    plant_id = Column(String(50), nullable=False)
    plant_name = Column(String(255))
    turbine_name = Column(String(100))
    generation = Column(Float(precision=2))
    avg_wind_speed = Column(Float(precision=2))
    generation_monthly = Column(Float(precision=2))
    avg_wind_speed_monthly = Column(Float(precision=2))
    edit_action = Column(String(100))
    reason_edit = Column(Text)
    edit_generation = Column(Float(precision=2))
    edit_avg_wind_speed = Column(Float(precision=2))
    edit_generation_monthly = Column(Float(precision=2))
    edit_avg_wind_speed_monthly = Column(Float(precision=2))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    def to_dict(self):
        return {
            "id": self.id,
            "date": self.date.isoformat() if self.date else None,
            "plant_id": self.plant_id,
            "plant_name": self.plant_name,
            "turbine_name": self.turbine_name,
            "generation": self.generation,
            "avg_wind_speed": self.avg_wind_speed,
            "generation_monthly": self.generation_monthly,
            "avg_wind_speed_monthly": self.avg_wind_speed_monthly,
            "edit_action": self.edit_action,
            "reason_edit": self.reason_edit,
            "edit_generation": self.edit_generation,
            "edit_avg_wind_speed": self.edit_avg_wind_speed,
            "edit_generation_monthly": self.edit_generation_monthly,
            "edit_avg_wind_speed_monthly": self.edit_avg_wind_speed_monthly,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }







class AdminUser(Base):
    __tablename__ = 'admin_users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(120), unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(120), unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }