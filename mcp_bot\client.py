#--------------------------
# USING LANGSMITH WITH PROPER TRACING
#--------------------------

import asyncio
import os
import sys
import uuid
from contextlib import AsyncExitStack
from dataclasses import dataclass
from typing import List, Optional, Any, Tuple
from datetime import datetime
from dotenv import load_dotenv

# --- MCP core (Python SDK) ---
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# --- LangChain / LangGraph stack ---
# Chat OpenAI adapter (you may swap with Google Gemini adapter if you prefer)
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import BaseMessage
from langchain.callbacks.base import BaseCallbackHandler

# langgraph / langchain agent builder
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent

# Lang<PERSON>mith tracing helpers
# We import the package and use ls.tracing_context(...) for robust scoping.
import langsmith as ls

# Local DB helpers (keep your actual implementations)
from DB.db_ops import insert_chat_history, fetch_chat_history, delete_chat_history

# Logging helper used by original code
from helper.logger_setup import setup_logger





# ----------------------------
# Logging setup
# ----------------------------
logger = setup_logger('client', 'client.log')

# ----------------------------
# Load environment & defaults
# ----------------------------
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    # In many environments ChatOpenAI picks up the OPENAI_API_KEY from env.
    # We still warn so developer is aware.
    logger.warning("OPENAI_API_KEY not found in env; ensure it is set if using OpenAI backend.")

OPENAI_MODEL = os.getenv("DEFAULT_MODEL", "gpt-4o-mini")


GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY not found in environment variables.")

GEMINI_MODEL = "gemini-2.0-flash-exp"

# ----------------------------
# Callback for tool logging (console friendly)
# ----------------------------
class ToolLoggingCallback(BaseCallbackHandler):
    """
    Minimal callback to print tool start/end. Safe to attach to LLM and tools.
    """

    def on_tool_start(self, serialized: dict, input_str: str, **kwargs) -> None:
        tool_name = serialized.get("name", "unknown_tool")
        # print to stdout so it appears in container logs / CloudWatch when running
        print(f"\n🛠️ LLM called tool: {tool_name}")
        print(f"   🔹 Input: {input_str}")

    def on_tool_end(self, output: Any, **kwargs) -> None:
        print(f"   🔸 Output: {output}\n")

# ----------------------------
# MCP server descriptor
# ----------------------------
@dataclass
class MCPServerSpec:
    """Defines an MCP server for stdio connection."""
    path: str
    args: Optional[List[str]] = None

    @property
    def command(self) -> str:
        # Assume python scripts end with .py; otherwise use node for js server
        return "python" if self.path.endswith(".py") else "node"

    @property
    def full_args(self) -> List[str]:
        return [self.path] + (self.args or [])

# ----------------------------
# Main MCPMultiServerClient
# ----------------------------
class MCPMultiServerClient:
    """
    Connects to one or more MCP servers (stdio), loads tools, creates a React-style agent,
    and uses LangSmith tracing_context to keep traces scoped to a specified LangSmith project.
    """

    def __init__(
        self,
        servers: List[MCPServerSpec],
        thread_id: Optional[str] = None,
        langsmith_project: str = "MCP-Client-Tracing",
    ):
        self.servers = servers
        self.exit_stack = AsyncExitStack()
        self.sessions: List[ClientSession] = []
        self.tools = []
        self.app = None

        # LangSmith project name to attribute traces to for this client
        self.langsmith_project = langsmith_project

        # Setup thread_id
        self.thread_id = thread_id or str(uuid.uuid4())

        # Load last 7 chat history messages from DB
        try:
            self.thread_history = fetch_chat_history(self.thread_id, last_n=7) or []
        except Exception as e:
            logger.error(f"[INIT] Failed to fetch chat history: {e}")
            self.thread_history = []

    # Internal helper to connect to one MCP server
    async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
        try:
            params = StdioServerParameters(command=spec.command, args=spec.full_args)
            read, write = await self.exit_stack.enter_async_context(stdio_client(params))
            session = await self.exit_stack.enter_async_context(ClientSession(read, write))
            await session.initialize()
            logger.info(f"[CONNECT] Connected to MCP server: {spec.path}")
            return session
        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
            raise

    async def connect(self) -> None:
        """
        Connect to all MCP servers, load their tools, and create the React agent with tracing scoped
        to self.langsmith_project during agent creation to ensure initialization traces go to the correct project.
        """

        # Validate LangSmith environment presence and log friendly warnings
        api_key = os.getenv("LANGSMITH_API_KEY") or os.getenv("LANGCHAIN_API_KEY")
        tracing_flag = os.getenv("LANGCHAIN_TRACING_V2", "").lower()

        if not api_key:
            logger.warning("[TRACE] LANGSMITH_API_KEY (or LANGCHAIN_API_KEY) not found in environment. Tracing may be disabled.")
        if tracing_flag != "true":
            logger.warning("[TRACE] LANGCHAIN_TRACING_V2 != 'true'. Ensure tracing v2 is enabled if you want LangSmith traces.")

        try:
            # Connect to each MCP server
            for spec in self.servers:
                session = await self._connect_one(spec)
                self.sessions.append(session)

            loaded_tools = []
            for s in self.sessions:
                maybe = load_mcp_tools(s)
                ts = await maybe if asyncio.iscoroutine(maybe) else maybe
                if ts:
                    loaded_tools.extend(ts)

            if not loaded_tools:
                logger.error("[TOOLS] No MCP tools found in any session")
                raise RuntimeError("No MCP tools found")

            self.tools = loaded_tools


            # Use LangSmith tracing_context to scope agent creation traces to our project.
            # tracing_context is safe as a synchronous context manager around async operations.
            with ls.tracing_context(project_name=self.langsmith_project, tags=["mcp-client", "agent-init"]):
                # Instantiate LLM

                
                # llm_openai = ChatOpenAI(
                #     model=OPENAI_MODEL,
                #     temperature=0,
                #     stream_usage=True,
                #     callbacks=[ToolLoggingCallback()],
                # )
                

                llm_gemini = ChatGoogleGenerativeAI(
                     model=GEMINI_MODEL,
                     temperature=0,
                     google_api_key=GEMINI_API_KEY,
                     callbacks=[ToolLoggingCallback()]
                )

                # llm_gemini = ChatGoogleGenerativeAI(
                #     model=GEMINI_MODEL,
                #     temperature=0,
                #     google_api_key=GEMINI_API_KEY,
                #     callbacks=[ToolLoggingCallback()],
                #     timeout=120,  # Increase timeout to 120 seconds
                #     max_retries=2,  # Enable retries
                # )

                # Attach callback to tools as well (useful for local console debugging)
                for tool in loaded_tools:
                    try:
                        tool.callbacks = [ToolLoggingCallback()]
                    except Exception:
                        # Not all tool objects may accept .callbacks; ignore gracefully
                        pass

                # Build a friendly tool description block used in system prompt
                tool_descriptions = []
                avalible_tools = []
                
                for t in loaded_tools:
                    desc = getattr(t, "description", "") or "No description provided"
                    avalible_tools.append(t.name)
                    tool_descriptions.append(f"tool_name: {t.name} -> tool_description: {desc}")
                print("Avalible tools: ", avalible_tools)
                tools_block = "\n".join(tool_descriptions)
                now = datetime.now()

                

                system_prompt = f"""
You are **Argus**, an expert Energy Data Analysis Agent for **Integrum Energy Infrastructure Ltd.** 
You provide clear, accurate, and concise energy-plant data to customers over WhatsApp using ONLY the
available, company-provided tools. Prioritize data security, traceability, and zero hallucination.

## CONTEXT
- Interaction channel: WhatsApp
- Customer key (internal): **{self.thread_id}**
- Current date (system): **{now.strftime('%Y-%m-%d')}**

- HARD RULE: The FIRST action for every user query MUST be a call to `get_metadata_by_contact_number(contact_number={self.thread_id})`.
  - Do this before any date resolution, metric lookup, or other tool calls.

--- 
## HARD CONSTRAINTS (MUST / MUST NOT)
1. **MUST** derive all factual outputs from tool responses. No guessing, approximating, or inventing values.
2. **MUST NOT** include any internal identifiers (phone numbers, `plant_id`, DB keys) in user-facing text. Use `plant_long_name` and `contact_person` only.
3. **MUST** perform an internal validation step after every tool call: check `status`, `data` presence, expected schema fields, and point out gaps.
4. **MUST** retry a failing or empty data tool call up to **2 additional times** (total **3 attempts**) with a short delay (2–3 seconds) between retries.
   - After each retry, validate whether the returned data is valid and non-empty.
   - If valid data is found, respond normally with the metric and its value.
   - If all 3 retries fail, return a professional fallback message:
     > "It seems there is no available data for this metric for the specified period. This might be due to data entry issues or missing records. Would you like me to check another parameter or timeframe?"
5. **MUST NOT** produce SQL, raw queries, or ad-hoc data-generation text. Use the provided tools for any DB access or computation.
6. **MUST NOT** expose chain-of-thought. Internal orchestration/reasoning is allowed for tool sequencing but must never be printed verbatim to users.
7. **MUST NOT** talk about what happened internally (the plan, tools used) in the user-facing response.

--- 
## REASONING / PLANNING (two-channel policy)
You are required to prepare two *distinct* artifacts per query **internally**:

A. **User-facing Plan (VISIBLE)** — a concise plan (max 3 bullets) that the user can see. Example:  
   The user-facing plan **must not include internal tool names, query functions, or DB calls**.
    - Only high-level, plain-language steps are allowed.
    - All tool names, query execution steps, and retries must remain in the internal <thinking> block.

B. **Internal <thinking> block (INTERNAL ONLY)** — a private orchestration block used for tool ordering, validation checks, retry logic, and ambiguity notes.  
   - **This block MUST NOT** be included in the user-facing response by the assistant.  
   - The system enforces this: internal logs may capture it for debugging but never surface it to users.

--- 
## STANDARD OPERATING PROCEDURE (SOP) — REQUIRED SEQUENCE
1. **Deconstruct & greet**
   - Parse intent, metrics, and date range. Resolve ambiguous dates using the date-resolver tool.
   - Greet the user by `contact_person` (from metadata). If `contact_person` is missing, use "Hello,".

2. **MANDATORY: Metadata lookup (FIRST CALL)**
   - Call: `get_metadata_by_contact_number(contact_number={self.thread_id})`
   - Validate response includes: `contact_person`, and `plants` array with each element containing `plant_long_name` and `plant_type`.
   - If missing fields, retry (up to 2 retries). If still missing, respond:  
     - "Hello — I couldn't retrieve your account metadata. Would you like me to retry or contact support?" (do not proceed to data queries)

3. **Produce User-facing Plan (visible)**
   - Provide 1–3 short bullets describing exactly which data will be fetched (e.g., generation, irradiation, PR, wind speed) and the target period or plant.
   - Example visible plan bullet: "Will fetch this month's generation for *[plant_long_name]*."

4. **Internal orchestration & tool calls**
   - Execute the planned tools in the declared order.
   - After each tool call:
     - Validate `status` and `data`. If partial, mark which fields are missing.
     - Log the tool call (tool name, sanitized params, timestamp, short status).
     - If the call fails, returns empty, or incomplete data, **retry** (up to 2 retries, total 2 attempts).
     - only retry if the tool call fails, not if the data is incomplete or no data is avalible.
     - If all retries fail, include the fallback message defined above.

5. **Synthesize final response**
   - The final message to the user must:
     - Start with greeting: "Hello [contact_person],"
     - Present only data derived from tools.
     - Show combined totals first if query spans multiple plants; then ask:  
       - "Would you like a plant-by-plant breakdown?"
     - Use Markdown tables or bullets for readability.
     - Do NOT include the internal `<thinking>` block.

--- 
## Trend Analysis:

- **Trend Analysis:**
act as an energy analyst who specializes in generating insightful summaries based on historical data trends. When asked for a "trend" (like "last 7 days," "last week," etc.), you must analyze the time series data and provide a concise summary that highlights patterns, anomalies, and actionable insights. Here's how to handle such cases:
  - If the user explicitly asks for a "trend" (e.g., last 7/10 days, last week, last month), you must:
    1. Fetch the relevant time-series data using the correct tool(s).
    2. Summarize the numerical results in a concise table or plot.
    3. Provide a short **analyst-style commentary** that makes sense and is actionable.  
       - Highlight overall pattern (e.g., stable, increasing, declining, fluctuating).  
       - Point out anomalies or spikes (e.g., "generation peaked on day 5").  
       - Phrase in natural, useful language, like a professional energy analyst briefing the customer.  
  - Example user query: *"What is my generation trend of last 10 days?"*  
    - Example response:  
      - "Hello Hari,   
         **Generation (last 10 days):** [table or chart here]  
         **Analysis:** Your generation has been mostly stable with an upward shift around Day 5, which recorded a significantly higher value compared to the previous days. The last three days have been slightly lower but still within normal variation."


--- 
## ERROR HANDLING (always use these templates)
- If metadata retrieval fails after retries:
  - "Hello — I couldn't load your account metadata after several attempts. Please try again in a few minutes or contact support. Would you like me to retry now?"
- If a data tool returns partial results:
  - "I retrieved partial data for [plant_long_name] (missing: POA). I can retry or show the partial results — which do you prefer?"
- If the user requests a date >= system date:
  - "I can only provide data up to yesterday ({(now - __import__('datetime').timedelta(days=1)).strftime('%Y-%m-%d')}). Please ask for a date on or before that."

--- 
## RESPONSE FORMAT (user-facing)
Start:
- Greeting (using contact_person)
- Results (values/tables) — all values must be derived from validated tool data
- Closing question (e.g., “Would you like a breakdown?” or “Send as Excel?”)

**Important:**  
Do NOT mention or describe internal tools, queries, or function names (e.g., `fetch_data_from_db`, `create_sql_query_poa_solar`, etc.).  
Instead, use natural-language provenance like this:


Dont expose about the internal tool names to the user.

Example snippet (user-facing):
> Hello Hari,  

> The total generation for Cloud9 this month is **229,570 kWh**.  

> Would you like a plant-wise breakdown?

---

## FINAL NOTES / PRINCIPLES
- Always use **verified** data from tools internally, but never disclose how it was fetched.
- All provenance references must use **plain-language phrasing** (e.g., “verified dataset,” “system records,” “official metrics”) — no internal tool names.
- Keep visible plans short and free of technical or sensitive details.
- The response should be short, precise, and professionally impressive.
- Internal `<thinking>` blocks and orchestration details are private — never surfaced to users.
- Decline speculative or unsupported queries politely:  
  “I can only report metrics available in the verified system data.”

"""
                # system_prompt = f"""
                #                 You are Argus — an energy data analysis agent for Integrum Energy Infrastructure Ltd.
                #                 Channel: WhatsApp
                #                 Customer ID: {self.thread_id}
                #                 Current date: {now.strftime('%Y-%m-%d')}

                #                 CORE RULES
                #                 1. Data integrity
                #                 - Source every value from tool responses only. No guessing or approximation.
                #                 - Hide internal IDs (phone numbers, plant_id, DB keys). Use plant_long_name and contact_person only.
                #                 - Validate each tool response (status, schema, required fields).
                #                 2. Retries
                #                 - On failure, retry up to 2 times (3 total) with 2–3s delay. If still failing, return the appropriate error template.
                #                 3. No internal exposure
                #                 - Never show SQL, tool names, function calls, logs, or orchestration details to the user.

                #                 MANDATORY FIRST ACTION
                #                 Always perform:
                #                 get_metadata_by_contact_number(contact_number={self.thread_id})
                #                 Validate response contains: contact_person and plants[] with plant_long_name and plant_type.
                #                 If missing after retries: reply: "Hello — I couldn't retrieve your account metadata. Retry or contact support?"

                #                 WORKFLOW (SOP)
                #                 1) Parse & greet
                #                 - Extract intent, metrics, date range. Resolve ambiguous dates with date-resolver (internal).
                #                 - Greet: "Hello [contact_person]," or "Hello," if missing.
                #                 2) User-facing plan (2–3 bullets)
                #                 - Plain-language bullets describing what will be fetched (no tools, no calls).
                #                 - Example: "• Fetch this month's generation for Solar Plant A"
                #                 3) Execute & validate (internal)
                #                 - Run tools in order, validate each response, retry if needed. Keep orchestration internal.
                #                 4) Synthesize user response
                #                 - Format:
                #                     Hello [contact_person],

                #                     [Validated results: short table or bullets — totals first for multi-plant queries]

                #                     Would you like a plant-by-plant breakdown?

                #                 TRENDS & ANALYSIS
                #                 - For trend requests: return time-series table/chart and concise analyst commentary:
                #                 • overall pattern (stable/increasing/declining)
                #                 • notable anomalies/peaks
                #                 • 1–2 actionable insights

                #                 ERROR TEMPLATES
                #                 - Metadata failure:
                #                 "I couldn't load your account metadata after several attempts. Retry or contact support?"
                #                 - Partial data:
                #                 "I retrieved partial data for [plant] (missing: POA). Show partial results or retry?"
                #                 - Future-date request:
                #                 "I can only provide data up to yesterday ({(now - __import__('datetime').timedelta(days=1)).strftime('%Y-%m-%d')}). Please request a date on or before that."
                #                 - No data after retries:
                #                 "No available data for this metric/period. This may be due to data entry issues. Check another parameter or timeframe?"

                #                 RESPONSE PRINCIPLES (DO / DON'T)
                #                 DO:
                #                 - Use only validated tool data.
                #                 - Keep responses short, precise, professional.
                #                 - Use Markdown tables/bullets for readability.
                #                 DON'T:
                #                 - Expose internal tool names, queries, or orchestration.
                #                 - Include chain-of-thought or internal logs.
                #                 - Provide data for dates >= current system date.

                #                 SHORT EXAMPLE
                #                 Hello Hari,

                #                 The total generation for Cloud9 this month is **229,570 kWh**.

                #                 Would you like a plant-wise breakdown?

                #                 """

                # Create the agent
                self.system_prompt = system_prompt  # Store system prompt for token counting

                self.app = create_react_agent(
                    model=llm_gemini,
                    tools=self.tools,
                    checkpointer=None,
                    prompt=system_prompt,
                )

                logger.info("[CONNECT] Agent created successfully (traced to project=%s)", self.langsmith_project)

        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
            raise

    async def ainvoke(self, user_text: str) -> str:
        """
        Invoke the agent with user_text. The entire invocation (which produces a run)
        is scoped inside a tracing_context so the run belongs to self.langsmith_project.
        """
        if self.app is None:
            logger.error("[INVOKE] Client not connected, app is None")
            raise RuntimeError("Client not connected. Call connect() first.")

        # Prepare state and config for agent
        messages_for_state: List[Tuple[str, str]] = [
            (e.get("role", "assistant"), e.get("content", "")) for e in self.thread_history
        ]
        messages_for_state.append(("user", user_text))
        state = {"messages": messages_for_state}
        config = {"configurable": {"thread_id": self.thread_id}}

        # Scope invocation to the LangSmith project using tracing_context
        with ls.tracing_context(project_name=self.langsmith_project, tags=["mcp-client", "invoke"]):
            try:
                # The agent's ainvoke is async; awaiting here produces a traced run.
                result = await self.app.ainvoke(state, config=config)
                logger.debug("[INVOKE] Raw result: %s", str(result))
            except Exception as e:
                logger.error(f"[INVOKE] Agent invocation failed: {e}")
                # Re-raise so caller can implement retry/backoff
                raise

        # Normalize the result to text
        content = "(no output)"
        if isinstance(result, dict):
            if "messages" in result and isinstance(result["messages"], list) and result["messages"]:
                last = result["messages"][-1]
                if isinstance(last, BaseMessage):
                    content = last.content
                elif isinstance(last, tuple) and len(last) >= 2:
                    content = last[1]
                elif isinstance(last, dict):
                    content = last.get("content") or last.get("text") or str(last)
                else:
                    content = str(last)
            elif "output" in result:
                content = str(result["output"])
            elif "text" in result:
                content = str(result["text"])
            else:
                content = str(result)
        elif isinstance(result, str):
            content = result
        else:
            # Fallback to stringification
            content = str(result)

        # --- Token counting and print statements ---
        try:
            import tiktoken
            # Use the correct encoding for the model
            encoding = tiktoken.encoding_for_model(OPENAI_MODEL)
        except Exception:
            encoding = None

        input_token_count = 0
        output_token_count = 0
        total_token_count = 0

        if encoding is not None:
            # Count tokens for system prompt, user_text, and output
            system_prompt = getattr(self, "system_prompt", "")
            input_text = (system_prompt or "") + "\n" + (user_text or "")
            input_token_count = len(encoding.encode(input_text))
            output_token_count = len(encoding.encode(content or ""))
            total_token_count = input_token_count + output_token_count

            print(f"[Token Count] Input tokens: {input_token_count}")
            print(f"[Token Count] Output tokens: {output_token_count}")
            print(f"[Token Count] Total tokens: {total_token_count}")
        else:
            print("[Token Count] tiktoken encoding not available for this model.")

        # Persist history to DB
        now = datetime.now().astimezone().isoformat()
        new_entries = [
            {"thread_id": self.thread_id, "role": "user", "content": user_text, "timestamp": now},
            {"thread_id": self.thread_id, "role": "assistant", "content": content, "timestamp": now},
        ]
        try:
            insert_chat_history(new_entries)
            self.thread_history.extend(new_entries)
        except Exception as e:
            logger.error(f"[DB] Failed to insert chat history: {e}")
            # Do not fail the whole operation because DB write failed; return the content.
        return content

    async def reset_history(self) -> None:
        """Clear persisted chat history for this thread."""
        try:
            delete_chat_history(self.thread_id)
            self.thread_history = []
            logger.info("[HISTORY] Reset history for thread_id=%s", self.thread_id)
        except Exception as e:
            logger.error(f"[HISTORY] Failed to reset history: {e}")
            raise

    async def close(self) -> None:
        """Close all sessions and exit contexts gracefully."""
        try:
            await self.exit_stack.aclose()
            logger.info("[CLOSE] Closed MCPMultiServerClient resources")
        except Exception as e:
            logger.warning(f"[CLOSE] Error during close: {e}")

    # Utility to pretty format stored ISO timestamps
    def _format_timestamp(self, iso_ts: Optional[str]) -> str:
        if not iso_ts:
            return ""
        try:
            dt = datetime.fromisoformat(iso_ts)
            return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
        except Exception as e:
            logger.error(f"[HISTORY] Failed to format timestamp: {e}")
            return iso_ts

    def format_history(self, last_n: Optional[int] = None) -> str:
        """Return a human readable text dump of last_n history entries."""
        n = last_n if last_n is not None else 7
        try:
            hist = fetch_chat_history(self.thread_id, last_n=n) or []
        except Exception as e:
            logger.error(f"[HISTORY] Failed to fetch chat history: {e}")
            hist = []
        lines = []
        for i, entry in enumerate(hist, start=1):
            role = entry.get("role", "assistant").upper()
            ts = self._format_timestamp(entry.get("timestamp", ""))
            header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
            body = entry.get("content", "")
            lines.append(header)
            lines.append(body)
            lines.append("-" * 80)
        return "\n".join(lines) if lines else "(no history)"
























#--------------------------------
# MCP CLIENT WITH  DATABASE AND ADVANCED
#--------------------------------



# import asyncio
# import os
# import sys
# import uuid
# from contextlib import AsyncExitStack
# from dataclasses import dataclass
# from typing import List, Optional
# from datetime import datetime

# from dotenv import load_dotenv

# # --- MCP core (Python SDK) ---
# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client

# # --- LangChain / LangGraph stack ---
# from langchain_openai import ChatOpenAI
# from langchain_mcp_adapters.tools import load_mcp_tools
# from langgraph.prebuilt import create_react_agent
# from langchain.schema import BaseMessage
# from langchain_google_genai import ChatGoogleGenerativeAI
# from DB.db_ops import insert_chat_history, fetch_chat_history, delete_chat_history
# from langchain.callbacks.base import BaseCallbackHandler
# from langsmith import tracing_context



# # ----------------------------
# # Logging setup
# # ----------------------------
# from helper.logger_setup import setup_logger
# logger = setup_logger('client', 'client.log')

# # ----------------------------
# # Config & Environment
# # ----------------------------
# load_dotenv()
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# if not OPENAI_API_KEY:
#     raise ValueError("OPENAI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = "gpt-4o"



# # GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# # if not GEMINI_API_KEY:
# #     raise ValueError("GEMINI_API_KEY not found in environment variables.")

# # DEFAULT_MODEL = "gemini-2.0-flash-exp"



# class ToolLoggingCallback(BaseCallbackHandler):
#     def on_tool_start(self, serialized, input_str, **kwargs):
#         tool_name = serialized.get("name", "unknown_tool")
#         print(f"\n🛠️ LLM called tool: {tool_name}")
#         print(f"   🔹 Input: {input_str}")

#     def on_tool_end(self, output, **kwargs):
#         print(f"   🔸 Output: {output}\n")


# @dataclass
# class MCPServerSpec:
#     """Defines an MCP server for stdio connection."""
#     path: str
#     args: Optional[List[str]] = None

#     @property
#     def command(self) -> str:
#         return "python" if self.path.endswith(".py") else "node"

#     @property
#     def full_args(self) -> List[str]:
#         return [self.path] + (self.args or [])


# class MCPMultiServerClient:
#     def __init__(self, servers: List[MCPServerSpec], thread_id: Optional[str] = None):
#         self.servers = servers
#         self.exit_stack = AsyncExitStack()
#         self.sessions: List[ClientSession] = []
#         self.tools = []
#         self.app = None

#         # Setup thread_id
#         if thread_id:
#             self.thread_id = thread_id
#         else:
#             self.thread_id = str(uuid.uuid4())

#         # Load last 7 chat history messages from DB
#         self.thread_history = fetch_chat_history(self.thread_id, last_n=7)


#     async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
#         try:
#             params = StdioServerParameters(command=spec.command, args=spec.full_args)
#             read, write = await self.exit_stack.enter_async_context(stdio_client(params))
#             session = await self.exit_stack.enter_async_context(ClientSession(read, write))
#             await session.initialize()
#             return session
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
#             raise

#     async def connect(self):
#         try:
#             for spec in self.servers:
#                 session = await self._connect_one(spec)
#                 self.sessions.append(session)

#             loaded_tools = []
#             for s in self.sessions:
#                 maybe = load_mcp_tools(s)
#                 ts = await maybe if asyncio.iscoroutine(maybe) else maybe
#                 if ts:
#                     loaded_tools.extend(ts)

#             if not loaded_tools:
#                 logger.error("[TOOLS] No MCP tools found in any session")
#                 raise RuntimeError("No MCP tools found")

#             self.tools = loaded_tools

#             # Instantiate LLM
#             with tracing_context(project_name="MCP-Client-Tracing", tags=["mcp-client"]):
#                 llm = ChatOpenAI(
#                     model=DEFAULT_MODEL,
#                     temperature=0,
#                     stream_usage=True,
#                     callbacks=[ToolLoggingCallback()],
#                 )
#             # llm = ChatOpenAI(model=DEFAULT_MODEL, 
#             #                  temperature=0, 
#             #                  stream_usage=True,
#             #                  callbacks=[ToolLoggingCallback()])

#             # llm = ChatGoogleGenerativeAI(
#             #         model=DEFAULT_MODEL,
#             #         temperature=0,
#             #         google_api_key=GEMINI_API_KEY,
#             #         callbacks=[ToolLoggingCallback()]
#             #     )

#             # Attach callback to tools as well
#             for tool in loaded_tools:
#                 tool.callbacks = [ToolLoggingCallback()]

#             tool_descriptions = []
#             for t in loaded_tools:
#                 desc = getattr(t, "description", "") or "No description provided"
#                 tool_descriptions.append(f"tool_name: {t.name} -> tool_description: {desc}")
#             print("TOOL DESCRIPTIONS: ", tool_descriptions)
#             tools_block = "\n".join(tool_descriptions)
#             now = datetime.now()
#             # Agent prompt
# #             system_prompt = f"""
# # You are an **Energy Data Analysis Agent** at **Integrum Energy Infrastructure Ltd.**, 
# # powered by MCP-provided tools.

# # This session is linked to a WhatsApp customer with mobile number: **{self.thread_id}**.  
# # - This number uniquely identifies the customer.  
# # - Always use it implicitly for plant-specific lookups.  
# # - **Never expose or repeat the mobile number** — instead, refer to the customer by their registered contact name.
# # Today's date is {now.strftime('%Y-%m-%d')}

# # Dont try to make the answers by yourself. always call the tools to get the data and then generate the answer.
# # understand the user’s query to determine the true intent. then think about how should i design the flows what are the tools i need call for solving that question

# # The following tools are available (discovered from the MCP servers at runtime):

# # {tools_block}

# # ---
# # always call this tool first get_plants_by_mobile_number to fetch the data then only use other tools. when ever user query comes
# # carefully analyze and understand the user’s query to determine the true intent. 
# # when ever user query comes call the tool get_plants_by_mobile_number and identify this is a hybrid plant or not based on the output from the tool need to call the hybrid functions properly
# # Then, call the most relevant tool(s) as needed and generate a clear, accurate response based on the results.

# # ### Your Objectives:
# # 1. **Clarify intent first** – ask follow-up questions when necessary.  
# # 2. **Prioritize tool usage** – always rely on MCP tools when available.  
# # 3. **Communicate professionally** – provide concise, structured responses (bullet points, tables, or JSON if suitable).  
# # 4. **Explain reasoning briefly** before making tool calls.  
# # 5. **Tool failures** – acknowledge the issue (e.g., “I wasn’t able to fetch that data just now. Would you like me to retry or check another metric?”).  
# # 6. **Respect confidentiality** – never reveal internal identifiers (e.g., `plant_id`, mobile numbers). Use only `plant_long_name` in customer-facing answers.  
# # 7. **Temporal limitation** – you cannot provide "today’s" values (e.g., today’s generation). You may only report **yesterday or earlier**.  
# # 8. **Multi-plant handling** –  
# #    - Use `get_plants_by_mobile_number` to check if this customer has more than one plant.  
# #    - If a query (e.g., "What was yesterday’s generation?") applies to all plants, first provide the **total combined generation**.  
# #    - Then politely ask: *“Would you like me to show the breakdown by each plant as well?”*  
# #    - Only show the breakdown if the customer requests it.  
# #    -    **Important:** The same WhatsApp number may be linked to multiple plants. Each plant has a unique `plant_id`, which must be respected for accurate tool lookups. 
# # 9. **Tone** – maintain a polite, professional style. Default to short answers unless detail is requested.  
# # 10. **Greet the customer by `contact_person`** if available; otherwise, keep greeting generic.  
# # Please ensure that your final response is crafted with the understanding that you are addressing a client.
# # ---
# # """

#             system_prompt = f"""
#             You are **Argus**, an expert Energy Data Analysis Agent for **Integrum Energy Infrastructure Ltd.** Your primary responsibility is to provide clients with clear, accurate, and concise data about their energy plants by using a suite of proprietary tools. You are professional, precise, and always prioritize data security.

# ## CONTEXT
# - **Interaction Channel:** WhatsApp
# - **Customer Identifier:** The session is linked to a customer with mobile number: **{self.thread_id}**. This is your internal key for all data lookups.
# - **Current Date:** Today's date is **{now.strftime('%Y-%m-%d')}**. You cannot provide data for this date; only for yesterday or earlier.
# - **Available Tools:** A list of tools is provided below. You MUST use them to answer all data-related questions.
# {tools_block}

# ---
# ## CORE DIRECTIVES
# 1.  **NEVER Guess:** Do not invent or calculate data. Your knowledge is limited to the output of the provided tools. If a query cannot be answered by the tools, state that clearly.
# 2.  **NEVER Expose Internal IDs:** NEVER mention the customer's mobile number, `plant_id`, or any other internal identifier in your response. Always refer to plants by their `plant_long_name` and the customer by their `contact_person` name.
# 3.  **ALWAYS Prioritize Tools:** Every response containing plant data MUST be derived from one or more tool calls.
# 4.  **STAY On-Topic:** Politely decline any requests not related to energy data analysis for the customer's registered plants.

# ---
# ## STANDARD OPERATING PROCEDURE (SOP)
# Follow this five-step process for every user query. You MUST first articulate your plan inside a `<thinking>` block before executing any tool calls.

# **Step 1: Deconstruct Query & Greet**
#    - Identify the core intent, metrics (e.g., generation, performance), and date range (e.g., yesterday, last month).
#    - Acknowledge the user's query.

# **Step 2: Initial Plant Lookup (Mandatory First Action)**
#    - Your FIRST action is ALWAYS to call the `get_plants_by_mobile_number()` tool.
#    - From its output, determine:
#      - The customer's `contact_person` name for greeting.
#      - The number of plants associated with the customer.
#      - The unique `plant_id` and `plant_long_name` for each plant.
#      - Whether any plant is a hybrid plant, which may require specific hybrid-related tools.

# **Step 3: Formulate a Plan**
#    - Based on the user's query and the data from Step 2, formulate a step-by-step plan within a `<thinking>` block.
#    - The plan must state which tool(s) you will call next and with what parameters (using the `plant_id`s).
#    - If there are multiple plants, your plan must include a step to aggregate the results first before presenting them.

# **Step 4: Execute Tool Calls**
#    - Sequentially call the tools identified in your plan.
#    - If a tool fails, inform the customer politely and ask if they would like you to retry.

# **Step 5: Synthesize and Respond**
#    - Consolidate the data from all tool calls into a single, clear, and professional response.
#    - Adhere strictly to the `Response Guidelines` below.

# ---
# ## RESPONSE GUIDELINES
# - **Greeting:** Always greet the customer by their `contact_person` name (e.g., "Hello [contact_person],"). If the name is unavailable, use a generic greeting like "Hello,".
# - **Formatting:** Use Markdown (bullet points, bolding, and tables) to structure information for readability.
# - **Tone:** Maintain a polite, helpful, and professional tone.
# - **Multi-Plant Logic:**
#     - If the query applies to multiple plants (e.g., "What was yesterday's generation?"), you MUST first provide the **total combined value** for all plants.
#     - After providing the total, you MUST ask: *“Would you like a detailed breakdown by plant?”*
#     - Only provide the plant-by-plant breakdown if the customer confirms.
# - **Confidentiality:** Reiterate: Never show the mobile number or `plant_id`. Use `plant_long_name`.

# ---
# ## CRITICAL EXAMPLE
# This example demonstrates the complete SOP for a multi-plant customer.

# **User Query:** "how much did we generate yesterday?"

# **Agent's Execution:**

# <thinking>
# 1.  **Deconstruct Query:** The user wants to know the total energy generation for "yesterday".
# 2.  **Initial Plant Lookup:** I must call `get_plants_by_mobile_number()` first to identify the customer's plants and name.
# 3.  **Formulate a Plan:**
#     - The tool `get_plants_by_mobile_number()` will return a list of plants. Let's assume it returns two plants: `plant_id: "P001"` and `plant_id: "P002"`. It also returns the `contact_person` as "Mr. Sharma".
#     - Since there are multiple plants, I need to get the generation for each one and then sum them up for the initial answer.
#     - I will call `get_generation_for_plant(plant_id="IN.INTE.__", date="YYYY-MM-DD")` and `get_generation_for_plant(plant_id="P002", date="YYYY-MM-DD")`.
#     - I will then add the results together.
#     - My final response will present the total and then ask if Mr. Sharma wants a breakdown.
# 4.  **Execute Tools:**
#     - Call `get_plants_by_mobile_number()`
#     - Call get_plant_type()
#     - call next tools accordingly based on user query
# 5.  **Synthesize and Respond:**
#     - Total generation = 1500 + 2500 = 4000 kWh.
#     - Craft the final response according to the guidelines.
# </thinking>

# **Agent's Final Response to User:**

# Hello Mr. Sharma,

# Yesterday, your plants generated a combined total of **4000 kWh**.

# Would you like a detailed breakdown by plant?

# --- 
#             """

#             self.app = create_react_agent(
#                 model=llm,
#                 tools=self.tools,
#                 checkpointer=None,
#                 prompt=system_prompt,
#             )
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
#             raise

#     async def ainvoke(self, user_text: str) -> str:
#         if self.app is None:
#             logger.error("[INVOKE] Client not connected, app is None")
#             raise RuntimeError("Client not connected. Call connect() first.")

#         # Use in-memory thread_history (always loaded from DB on __init__ and after reset)
#         messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in self.thread_history]
#         messages_for_state.append(("user", user_text))
#         state = {"messages": messages_for_state}
#         config = {"configurable": {"thread_id": self.thread_id}}

#         try:
#             result = await self.app.ainvoke(state, config=config)
#             print(f"result: {result}")
#         except Exception as e:
#             logger.error(f"[INVOKE] Agent invocation failed: {e}")
#             raise

#         # Normalize result
#         content = "(no output)"
#         if isinstance(result, dict):
#             if "messages" in result and isinstance(result["messages"], list):
#                 last = result["messages"][-1]
#                 if isinstance(last, BaseMessage):
#                     content = last.content
#                 elif isinstance(last, tuple) and len(last) >= 2:
#                     content = last[1]
#                 elif isinstance(last, dict):
#                     content = last.get("content") or last.get("text")
#                 else:
#                     content = str(last)
#             elif "output" in result:
#                 content = str(result["output"])
#             elif "text" in result:
#                 content = str(result["text"])
#         elif isinstance(result, str):
#             content = result

#         # Persist history to DB
#         now = datetime.now().astimezone().isoformat()
#         new_entries = [
#             {"thread_id": self.thread_id, "role": "user", "content": user_text, "timestamp": now},
#             {"thread_id": self.thread_id, "role": "assistant", "content": content, "timestamp": now},
#         ]
#         insert_chat_history(new_entries)
#         self.thread_history.extend(new_entries)

#         return content

#     async def reset_history(self):
#         delete_chat_history(self.thread_id)
#         self.thread_history = []

#     async def close(self):
#         await self.exit_stack.aclose()

#     def _format_timestamp(self, iso_ts: Optional[str]) -> str:
#         if not iso_ts:
#             return ""
#         try:
#             dt = datetime.fromisoformat(iso_ts)
#             return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
#         except Exception as e:
#             logger.error(f"[HISTORY] Failed to format timestamp: {e}")
#             return iso_ts

#     def format_history(self, last_n: Optional[int] = None) -> str:
#         # Always fetch latest from DB, default to last 7
#         n = last_n if last_n is not None else 7
#         hist = fetch_chat_history(self.thread_id, last_n=n)
#         lines = []
#         for i, entry in enumerate(hist, start=1):
#             role = entry.get("role", "assistant").upper()
#             ts = self._format_timestamp(entry.get("timestamp", ""))
#             header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
#             body = entry.get("content", "")
#             lines.append(header)
#             lines.append(body)
#             lines.append("-" * 80)
#         return "\n".join(lines) if lines else "(no history)"
    





#-----------------------------------
# MCP CLIENT WITHOUT DATABASE
#-----------------------------------


# import asyncio
# import os
# import sys
# import uuid
# import json
# from contextlib import AsyncExitStack
# from dataclasses import dataclass
# from typing import List, Optional
# from datetime import datetime

# from dotenv import load_dotenv

# # --- MCP core (Python SDK) ---
# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client

# # --- LangChain / LangGraph stack ---
# from langchain_openai import ChatOpenAI
# from langchain_mcp_adapters.tools import load_mcp_tools
# from langgraph.prebuilt import create_react_agent
# from langchain.schema import BaseMessage



# # ----------------------------
# # Logging setup
# # ----------------------------
# from helper.logger_setup import setup_logger
# logger = setup_logger('client', 'client.log')

# # ----------------------------
# # Config & Environment
# # ----------------------------
# load_dotenv()
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# if not OPENAI_API_KEY:
#     raise ValueError("OPENAI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = "gpt-4o"
# DEFAULT_MEMORY_FILE =  "mcp_bot/mcp_client_memory.json"

# from langchain.callbacks.base import BaseCallbackHandler

# class ToolLoggingCallback(BaseCallbackHandler):
#     def on_tool_start(self, serialized, input_str, **kwargs):
#         tool_name = serialized.get("name", "unknown_tool")
#         print(f"\n🛠️ LLM called tool: {tool_name}")
#         print(f"   🔹 Input: {input_str}")

#     def on_tool_end(self, output, **kwargs):
#         print(f"   🔸 Output: {output}\n")


# @dataclass
# class MCPServerSpec:
#     """Defines an MCP server for stdio connection."""
#     path: str
#     args: Optional[List[str]] = None

#     @property
#     def command(self) -> str:
#         return "python" if self.path.endswith(".py") else "node"

#     @property
#     def full_args(self) -> List[str]:
#         return [self.path] + (self.args or [])


# class MCPMultiServerClient:
#     def __init__(self, servers: List[MCPServerSpec], thread_id: Optional[str] = None):
#         # Only log errors and critical lifecycle events
#         self.servers = servers
#         self.exit_stack = AsyncExitStack()
#         self.sessions: List[ClientSession] = []
#         self.tools = []
#         self.app = None

#         # Setup memory file
#         self.memory_file = DEFAULT_MEMORY_FILE
#         os.makedirs(os.path.dirname(self.memory_file) or ".", exist_ok=True)

#         if os.path.exists(self.memory_file):
#             try:
#                 with open(self.memory_file, "r", encoding="utf-8") as f:
#                     self.memory = json.load(f)
#             except Exception as e:
#                 logger.error(f"[MEMORY] Failed to load memory file: {e}")
#                 self.memory = {}
#         else:
#             self.memory = {}

#         # ✅ Use thread_id from constructor OR memory OR generate new
#         if thread_id:
#             self.thread_id = thread_id
#         else:
#             self.thread_id = self.memory.get("thread_id", str(uuid.uuid4()))

#         self.memory.setdefault(self.thread_id, [])
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()


#     def save_memory(self):
#         with open(self.memory_file, "w", encoding="utf-8") as f:
#             json.dump(self.memory, f, indent=2)

#     async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
#         try:
#             params = StdioServerParameters(command=spec.command, args=spec.full_args)
#             read, write = await self.exit_stack.enter_async_context(stdio_client(params))
#             session = await self.exit_stack.enter_async_context(ClientSession(read, write))
#             await session.initialize()
#             return session
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
#             raise

#     async def connect(self):
#         try:
#             for spec in self.servers:
#                 session = await self._connect_one(spec)
#                 self.sessions.append(session)

#             loaded_tools = []
#             for s in self.sessions:
#                 maybe = load_mcp_tools(s)
#                 ts = await maybe if asyncio.iscoroutine(maybe) else maybe
#                 if ts:
#                     loaded_tools.extend(ts)

#             if not loaded_tools:
#                 logger.error("[TOOLS] No MCP tools found in any session")
#                 raise RuntimeError("No MCP tools found")

#             self.tools = loaded_tools

#             # Instantiate LLM
#             llm = ChatOpenAI(model=DEFAULT_MODEL, temperature=0, callbacks=[ToolLoggingCallback()])

#             # Attach callback to tools as well
#             for tool in loaded_tools:
#                 tool.callbacks = [ToolLoggingCallback()]

#             tool_descriptions = []
#             for t in loaded_tools:
#                 desc = getattr(t, "description", "") or "No description provided"
#                 tool_descriptions.append(f"- **{t.name}**: {desc}")

#             tools_block = "\n".join(tool_descriptions)
#             now = datetime.now()
#             # Agent prompt
#             system_prompt = f"""
# You are an **Energy Data Analysis Agent** at **Integrum Energy Infrastructure Ltd.**, 
# powered by MCP-provided tools.

# 📱 This session is linked to a WhatsApp customer with mobile number: **{self.thread_id}**.  
# - This number uniquely identifies the customer.  
# - Always use it implicitly for plant-specific lookups.  
# - **Never expose or repeat the mobile number** — instead, refer to the customer by their registered contact name.



# Today's date is {now.strftime('%Y-%m-%d')}


# The following tools are available (discovered from the MCP servers at runtime):

# {tools_block}

# ---
# always call this tool first get_plants_by_mobile_number to fetch the data then only use other tools. when ever user query comes
# carefully analyze and understand the user’s query to determine the true intent. 
# Then, call the most relevant tool(s) as needed and generate a clear, accurate response based on the results.

# ### 🎯 Your Objectives:
# 1. **Clarify intent first** – ask follow-up questions when necessary.  
# 2. **Prioritize tool usage** – always rely on MCP tools when available.  
# 3. **Communicate professionally** – provide concise, structured responses (bullet points, tables, or JSON if suitable).  
# 4. **Explain reasoning briefly** before making tool calls.  
# 5. **Tool failures** – acknowledge the issue (e.g., “I wasn’t able to fetch that data just now. Would you like me to retry or check another metric?”).  
# 6. **Respect confidentiality** – never reveal internal identifiers (e.g., `plant_id`, mobile numbers). Use only `plant_long_name` in customer-facing answers.  
# 7. **Temporal limitation** – you cannot provide "today’s" values (e.g., today’s generation). You may only report **yesterday or earlier**.  
# 8. **Multi-plant handling** –  
#    - Use `get_plants_by_mobile_number` to check if this customer has more than one plant.  
#    - If a query (e.g., "What was yesterday’s generation?") applies to all plants, first provide the **total combined generation**.  
#    - Then politely ask: *“Would you like me to show the breakdown by each plant as well?”*  
#    - Only show the breakdown if the customer requests it.  
#    - ⚡ **Important:** The same WhatsApp number may be linked to multiple plants. Each plant has a unique `plant_id`, which must be respected for accurate tool lookups. 
# 9. **Tone** – maintain a polite, professional style. Default to short answers unless detail is requested.  
# 10. **Greet the customer by `contact_person`** if available; otherwise, keep greeting generic.  
# ---
# """

#             self.app = create_react_agent(
#                 model=llm,
#                 tools=self.tools,
#                 checkpointer=None,
#                 prompt=system_prompt,
#             )
#         except Exception as e:
#             logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
#             raise

#     async def ainvoke(self, user_text: str) -> str:
#         if self.app is None:
#             logger.error("[INVOKE] Client not connected, app is None")
#             raise RuntimeError("Client not connected. Call connect() first.")

#         thread_history = self.memory.get(self.thread_id, []) or []

#         messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in thread_history]
#         messages_for_state.append(("user", user_text))
#         state = {"messages": messages_for_state}
#         config = {"configurable": {"thread_id": self.thread_id}}

#         try:
#             result = await self.app.ainvoke(state, config=config)
#         except Exception as e:
#             logger.error(f"[INVOKE] Agent invocation failed: {e}")
#             raise

#         # Normalize result
#         content = "(no output)"
#         if isinstance(result, dict):
#             if "messages" in result and isinstance(result["messages"], list):
#                 last = result["messages"][-1]
#                 if isinstance(last, BaseMessage):
#                     content = last.content
#                 elif isinstance(last, tuple) and len(last) >= 2:
#                     content = last[1]
#                 elif isinstance(last, dict):
#                     content = last.get("content") or last.get("text")
#                 else:
#                     content = str(last)
#             elif "output" in result:
#                 content = str(result["output"])
#             elif "text" in result:
#                 content = str(result["text"])
#         elif isinstance(result, str):
#             content = result

#         # Persist history
#         now = datetime.now().astimezone().isoformat()
#         thread_history.append({"role": "user", "content": user_text, "timestamp": now})
#         thread_history.append({"role": "assistant", "content": content, "timestamp": now})

#         self.memory[self.thread_id] = thread_history
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()

#         return content

#     async def reset_history(self):
#         self.memory[self.thread_id] = []
#         self.save_memory()

#     async def close(self):
#         await self.exit_stack.aclose()
#         self.save_memory()

#     def _format_timestamp(self, iso_ts: Optional[str]) -> str:
#         if not iso_ts:
#             return ""
#         try:
#             dt = datetime.fromisoformat(iso_ts)
#             return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
#         except Exception as e:
#             logger.error(f"[HISTORY] Failed to format timestamp: {e}")
#             return iso_ts

#     def format_history(self, last_n: Optional[int] = None) -> str:
#         hist = self.memory.get(self.thread_id, []) or []
#         if last_n is not None:
#             hist = hist[-last_n:]
#         lines = []
#         for i, entry in enumerate(hist, start=1):
#             role = entry.get("role", "assistant").upper()
#             ts = self._format_timestamp(entry.get("timestamp", ""))
#             header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
#             body = entry.get("content", "")
#             lines.append(header)
#             lines.append(body)
#             lines.append("-" * 80)
#         return "\n".join(lines) if lines else "(no history)"
