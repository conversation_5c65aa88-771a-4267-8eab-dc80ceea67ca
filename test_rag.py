# from mcp_bot.rag_system import RAGRetrieverFAISS, generate_answer, run_evaluation_example
# import logging as logger
# import os




# # --------------------- main (example) ---------------------
# if __name__ == "__main__":
#     rag = RAGRetrieverFAISS()

#     # Ingest initial txt file
#     txt_path = os.path.join("mcp_bot", "integrum_energy.txt")
#     rag.ingest_file(txt_path)

#     # Build or load FAISS index
#     rag.build_or_load_index()

#     # Example interactive query
#     query = "Who is the COO of Integrum Energy?"
#     top_docs = rag.retrieve(query, k=5, mode="hybrid")
#     context = rag.assemble_context(top_docs)

#     if top_docs:
#         answer = generate_answer(query, context, rag.openai_client)
#         print(f"\n[ANSWER]: {answer}")
#     else:
#         logger.info("\n[ANSWER]: I don't know")

#     # ---------- Example evaluation run (you should replace with your labeled testset) ----------
#     # testset example: list of maps with 'query' and 'ground_truth_doc_ids' (indices into rag.docs)
#     example_testset = [
#         {"query": "Who is the COO of Integrum Energy?", "ground_truth_doc_ids": [3]},
#         # add your labeled queries here
#     ]
#     eval_metrics = run_evaluation_example(rag, example_testset)
#     logger.info(f"Eval summary: {eval_metrics}")



from app.import_turbine_inverter_data import import_turbine_inverter_edits
if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python import_turbine_inverter_data.py <excel_file_path>")
    else:
        try:
            total = import_turbine_inverter_edits(sys.argv[1])
            print("Finished. Total turbine/inverter updates: %d", total)
        except Exception:
            print("Import failed.")
            sys.exit(1)
