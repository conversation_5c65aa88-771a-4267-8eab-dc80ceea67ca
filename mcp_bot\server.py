import sys
from typing import Any, List, Dict, Optional
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import re
import json
import pandas as pd
from datetime import datetime
from sqlalchemy import text
from mcp.server.fastmcp import FastMCP, Image
from datetime import date, timedelta
from mcp_bot.mcp_db_setup import engine
from helper.logger_setup import setup_logger
from config.settings import Config
from helper.integration_utilities import PrescintoIntegrationUtilities
from whatsapp.sender_whatsapp import send_whatsapp_document_mcp, upload_whatsapp_media, delete_whatsapp_media, send_whatsapp_document
import time
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns

from mcp_bot.rag_system import RAGRetrieverFAISS
    

# =========================
# Configuration & Constants
# =========================

# Environment variables for configuration
CUSTOMER_CSV = Config.CUSTOMER_DATA_CSV_PATH
PLANT_PREFIX = "IN.INTE."


ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
EXPORT_DIR = os.path.join(ROOT_DIR, "exports")
PLOT_EXPORT_DIR = os.path.join(ROOT_DIR, "export_plots")

os.makedirs(EXPORT_DIR, exist_ok=True)
os.makedirs(PLOT_EXPORT_DIR, exist_ok=True)


# =========================
# Logging Setup
# =========================

logger = setup_logger("mcp_bot.server", "mcp_server.log")

# =========================
# Initialize Prescinto integration
# =========================

INTEGRATION_SERVER = "IN"
INTEGRATION_TOKEN  = os.getenv("PRESCINTO_TOKEN")
integration = PrescintoIntegrationUtilities(server=INTEGRATION_SERVER, token=INTEGRATION_TOKEN)




# =========================
# FastMCP Server Init
# =========================

mcp = FastMCP("mcp_dgr_bot")


# ---------------------------
# Helper functions
# ---------------------------

def _extract_plant_name(sql: str) -> str:
    """Extract plant_short_name from SQL query if present."""
    match = re.search(r"plant_short_name\s*=\s*'([^']+)'", sql, re.IGNORECASE)
    return match.group(1) if match else None


def _extract_metric(sql: str) -> str:
    """
    Extract a representative metric/column from SELECT clause.
    Skips common identifiers like plant_short_name, plant_long_name, and date.
    """
    select_match = re.search(r"SELECT\s+(.*?)\s+FROM", sql, re.IGNORECASE | re.DOTALL)
    if not select_match:
        return None

    select_fields = select_match.group(1)
    fields = [f.strip() for f in re.split(r",|\n", select_fields) if f.strip()]

    for f in fields:
        lower_f = f.lower()
        if any(x in lower_f for x in ["plant_short_name", "plant_long_name", "date"]):
            continue

        alias_match = re.search(r"AS\s+([A-Z0-9_]+)", f, re.IGNORECASE)
        if alias_match:
            return alias_match.group(1)

        # Fall back to first token (cleaned up)
        return re.sub(r"[^a-zA-Z0-9]", "", f.split()[0])

    return None
    

def _validate_date_str(date_str: str) -> str:
    if re.match(r"^\d{4}-\d{2}-\d{2}$", date_str):
        return date_str
    raise ValueError(f"Invalid date format: {date_str}")



def _save_plot(fig, prefix: str) -> str:
    """Save plot to EXPORT_DIR and return path."""
    os.makedirs(PLOT_EXPORT_DIR, exist_ok=True)
    filename = os.path.join(PLOT_EXPORT_DIR, f"{prefix}_{int(time.time())}.png")
    fig.savefig(filename, dpi=300, bbox_inches="tight")
    plt.close(fig)
    return filename

def _plot_metric_line(
    json_input: str,
    value_column: str,
    title: str,
    y_label: str,
    filename_prefix: str,
    incoming_number: str,
    reference_lines: list = None,
    legend_title: str = "Plant"
) -> dict:
    """
    Professional plotting for plant metrics.
    Automatically handles hybrid plants, multiple plants, reference lines, and WhatsApp sending.
    """
    try:
        df = _prepare_dataframe(json_input, value_column)
        if df.empty:
            raise ValueError("No data available to plot")

        # Handle plant type if provided
        records = json.loads(json_input)
        if records and isinstance(records, list) and "type" in records[0]:
            df["plant_type"] = [rec.get("type", "") for rec in records]
        else:
            df["plant_type"] = ""

        # Label for legend
        if df["plant_type"].nunique() > 1:
            df["plant_label"] = df["plant_long_name"] + " (" + df["plant_type"].str.capitalize() + ")"
            legend_col = "plant_label"
            legend_title = "Plant (Type)"
        else:
            legend_col = "plant_long_name"

        sns.set(style="whitegrid", context="talk", font_scale=1.1)
        fig, ax = plt.subplots(figsize=(14, 7))
        
        sns.lineplot(
            data=df, 
            x="date", y="value", hue=legend_col, 
            marker="o", linewidth=2.0, palette="tab10", ax=ax
        )

        # Reference lines
        if reference_lines:
            for ref in reference_lines:
                ax.axhline(
                    y=ref["y"], color=ref.get("color", "red"),
                    linestyle=ref.get("linestyle", "--"), label=ref.get("label")
                )

        # Dynamic title
        plot_title = f"{title} (Hybrid)" if df["plant_type"].nunique() > 1 else title
        ax.set_title(plot_title, fontsize=18, weight="bold")
        ax.set_xlabel("Date", fontsize=14)
        ax.set_ylabel(y_label, fontsize=14)
        ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.xticks(rotation=45, ha='right')

        # Legend management
        if df[legend_col].nunique() > 1 or reference_lines:
            ax.legend(title=legend_title, bbox_to_anchor=(1.05, 1), loc="upper left")
        else:
            if ax.legend_:
                ax.legend_.remove()

        # Optional: annotate max/min
        for _, row in df.iterrows():
            ax.annotate(f"{row['value']:.1f}", (row['date'], row['value']),
                        textcoords="offset points", xytext=(0,5), ha='center', fontsize=9)

        # Save and send plot
        path = _save_plot(fig, filename_prefix)
        media_id = upload_whatsapp_media(path)
        send_whatsapp_document(
            recipient_number = "91" + incoming_number, 
            image_id_or_url = media_id, 
            file_name = filename_prefix)
        return {"status": "success"}

    except Exception as e:
        print(f"[ERROR] Plotting failed: {str(e)}")
        return {"status": "failed", "error": str(e)}



def _prepare_dataframe(json_input: str, value_column: str) -> pd.DataFrame:
    """
    Prepares a DataFrame for plotting:
    - Converts nested JSON or DB timestamps to proper datetime
    - Ensures 'value' column exists
    """
    try:
        # Parse JSON
        if isinstance(json_input, str):
            obj = json.loads(json_input)
        else:
            obj = json_input

        # Flatten nested 'data' if present
        if "data" in obj:
            records = obj["data"]
            plant_name = obj.get("plant_long_name", "Unknown Plant")
            for r in records:
                r["plant_long_name"] = plant_name
        elif isinstance(obj, list):
            records = obj
        else:
            records = [obj]

        df = pd.DataFrame(records)
        if df.empty:
            return df

        # Convert timestamps (if in ms) to datetime
        if df['date'].dtype in [int, float]:
            df['date'] = pd.to_datetime(df['date'], unit='ms')
        else:
            df['date'] = pd.to_datetime(df['date'])

        # Ensure the value column exists
        if value_column not in df.columns:
            raise KeyError(f"'{value_column}' column missing in input data")
        df['value'] = df[value_column]

        return df

    except Exception as e:
        print(f"[ERROR] Preparing DataFrame failed: {e}")
        return pd.DataFrame()




# =========================
# MCP Tools
# =========================

def normalize_number(number: str) -> str:
    """Remove all non-digits from a phone number string."""
    return re.sub(r"\D", "", str(number).strip()) if number else ""

@mcp.tool(
    name="get_metadata_by_contact_number",
    description=(
        "Looks up and returns detailed metadata for all renewable energy plants associated with a given contact number. "
        "Searches the customer CSV for matches and provides plant ID, client name, type, capacity, turbine metadata, and mapped contact person. "
        "Call this tool at the start of every conversation to identify the user's plants and context. "
        "If the report has the same combined name, then it should be considered a hybrid plant. "
        "If a plant is identified as a hybrid (same combined name), you should call the hybrid tools for other tasks."
    )
)
def get_metadata_by_contact_number(input_number: str) -> List[Dict[str, Any]]:
    """
    Retrieves comprehensive metadata for renewable energy plants by searching the customer CSV for records where the provided contact number matches entries in the 'Contact Number' or 'Test Number' columns.

    Args:
        input_number (str): The contact number to search for. Can include spaces, dashes, or other non-digit characters.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, each containing metadata for a plant associated with the input number.
        Each dictionary includes:
            - 'plant_id' (str): The plant's unique identifier.
            - 'client_name' (str): The name of the customer/client.
            - 'type' (str): The type of plant (e.g., 'solar', 'wind').
            - 'capacity' (str): The plant's capacity in MW.
            - 'combined' (str): Combined plant information, if available.
            - 'turbine_metadata' (str): Turbine metadata, if available.
            - 'contact_person' (str): The mapped contact person for the matched number, or a default if not found.
            - 'contact_number' (str): The raw contact number(s) from the CSV.
            - 'test_number' (str): The raw test number(s) from the CSV.

    Example:
        >>> get_metadata_by_contact_number("9876543210")
        [
            {
                "plant_id": "IN.INTE.KIDS",
                "client_name": "ABC Energy",
                "type": "solar",
                "capacity": "5",
                "combined": "",
                "turbine_metadata": "",
                "contact_person": "John Doe",
                "contact_number": "9876543210",
                "test_number": ""
            }
        ]

    Notes:
        - Handles multiple matches and ensures contact person mapping if found in 'Contact Number'.
        - If the input number is found only in 'Test Number', 'contact_person' is set to "Test Number Contact".
        - Returns an empty list if no matches are found.

    """
    file_path = CUSTOMER_CSV

    # Normalize input number
    input_number = normalize_number(input_number)

    # Load CSV
    df = pd.read_csv(file_path, dtype=str).fillna("")

    results: List[Dict[str, Any]] = []

    for _, row in df.iterrows():
        contact_numbers_raw = str(row.get("Contact Number", ""))
        test_numbers_raw = str(row.get("Test Number", ""))
        contact_persons_raw = str(row.get("Contact Person", ""))

        # Normalize and split contact/test numbers
        contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(",") if normalize_number(num)]
        # test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(",") if normalize_number(num)]
        contact_persons_list = [name.strip() for name in contact_persons_raw.split(",") if name.strip()]

        all_numbers = contact_numbers_list
        if input_number in all_numbers:
            # Map contact number to corresponding contact person
            if input_number in contact_numbers_list:
                idx = contact_numbers_list.index(input_number)
                matched_person = (
                    contact_persons_list[idx]
                    if idx < len(contact_persons_list)
                    else (contact_persons_list[-1] if contact_persons_list else "")
                )
            else:
                matched_person = "Test Number Contact"

            result = {
                "plant_id": str(row.get("Plant id", "")).strip(),
                "client_name": str(row.get("Customer Name", "")).strip(),
                "type": str(row.get("Type", "")).strip(),
                "capacity": str(row.get("Capacity ( MW)", "")).strip(),
                "combined": str(row.get("Combined", "")).strip(),
                "turbine_metadata": str(row.get("Turbine Metadata", "")).strip(),
                "contact_person": matched_person,
                "contact_number": contact_numbers_raw.strip(),
                "test_number": test_numbers_raw.strip(),
            }
            results.append(result)

    if not results:
        return []


    return results



@mcp.tool(
    name="get_plant_type",
    description="Returns the type of plant ('solar' or 'wind') for a given plant_id by looking it up in the customer CSV. Use to determine plant category for further queries."

)
def get_plant_type(plant_id: str) -> str:
    """
    Returns the type of plant ('solar' or 'wind') for a given plant_id from the customer CSV.

    Args:
        plant_id (str): The plant ID to look up.

    Returns:
        str: The type of the plant. Either 'solar' or 'wind'.

    Example:
        >>> get_plant_type("IN.INTE.KIDS")
        'solar'

    Raises:
        FileNotFoundError: If the customer CSV file is not found.
        KeyError: If the 'plant id' column is missing in the CSV.
        ValueError: If the plant_id is not found in the CSV.
    """
    if not os.path.isfile(CUSTOMER_CSV):
        raise FileNotFoundError(f"CSV not found at {CUSTOMER_CSV}")
    df = pd.read_csv(CUSTOMER_CSV)
    df.columns = df.columns.str.strip().str.lower()
    if 'plant id' not in df.columns:
        raise KeyError("Column 'plant id' missing in CSV")
    match = df[df['plant id'] == plant_id]
    if match.empty:
        raise ValueError(f"Plant ID '{plant_id}' not found")
    return match['type'].iloc[0]



@mcp.tool(
    name="get_company_qa_rag",
    description=(
        "Provides authoritative answers to company-related questions about Integrum Energy using a Retrieval-Augmented Generation (RAG) system. "
        "This tool searches a vector database of company documents to retrieve the most relevant information and generates a context-rich response. "
        "Use this tool for factual queries about company leadership, executive roles (e.g., CEO, COO, Founder), organizational structure, or other official company facts. "
        "Returns a synthesized answer based on the most relevant documents."
    )
)
def get_company_qa_rag(question: str) -> str:
    """
    Answers company-related questions about Integrum Energy using a Retrieval-Augmented Generation (RAG) approach.

    Args:
        question (str): A factual question regarding Integrum Energy, such as leadership, executive roles, or company facts.

    Returns:
        str: A synthesized, context-based answer generated from the most relevant company documents.

    Notes:
        - This tool leverages a vector database to retrieve and assemble the most pertinent information.
        - It is intended for professional, fact-based queries about Integrum Energy's organization, leadership, or official company details.
        - For best results, provide clear and specific questions.
    """
    logger.info(f"get_company_qa_rag called with question={question}")
    rag = RAGRetrieverFAISS()
    # Ingest initial txt file
    txt_path = os.path.join("mcp_bot", "integrum_energy.txt")
    rag.ingest_file(txt_path)
    logger.info(f"txt_path={txt_path}")

    # Build or load FAISS index
    rag.build_or_load_index()
    logger.info(f"FAISS index built or loaded.")


    top_docs = rag.retrieve(question, k=5, mode="hybrid")
    logger.info(f"Top docs: {top_docs}")
    context = rag.assemble_context(top_docs)
    logger.info(f"Context: {context}")
    return context



# @mcp.tool(
#     name="create_sql_query_generation_solar",
#     description=(
#         "Generate SQL queries to fetch solar plant generation metrics from the DGR database "
#         "using the 'edit_generation' column. Supported metric types include: "
#         "- 'today': Daily generation for the current date "
#         "- 'yesterday': Daily generation for the previous date "
#         "- 'monthly_total': Sum of generation from the first day of the current month up to today for only the current month not previous month"
#         "- 'monthly_average': Average daily generation from the first day of the current month up to today "
#         "- 'highest_day_month': Day with the highest generation in the current month "
#         "- 'lowest_day_month': Day with the lowest generation in the current month "
#         "- 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today) "
#         "- 'lifetime_total': Total generation across all available records "
#         "- 'trend': Daily generation trend for the last N days (requires 'days' parameter). "
#         "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter). "
#         "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter)."
#     )
# )
# def create_sql_query_generation_solar(plant_id: str, metric_type: str, days: int = None) -> str:
#     """
#     Generate SQL queries to fetch solar plant generation metrics from the DGR database.

#     Args:
#         plant_id (str): Short name of the solar plant (e.g., 'IN.INTE.KIDS').
#                        Plant IDs always start with 'IN.INTE.'
#         metric_type (str): Type of generation metric to fetch. Supported values:
#             - 'today': Daily generation for the current date
#             - 'yesterday': Daily generation for the previous date
#             - 'monthly_total': Sum of generation from the first day of the current month up to today for only the current not the last month
#             - 'monthly_average': Average daily generation from the first day of the current month up to today
#             - 'highest_day_month': Day with the highest generation in the current month
#             - 'lowest_day_month': Day with the lowest generation in the current month
#             - 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today)
#             - 'lifetime_total': Total generation across all available records
#             - 'trend': Daily generation trend for the last N days (requires 'days' parameter)
#             - 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter)
#             - 'last_n_days_total': Total generation for the last N days (requires 'days' parameter)
#         days (int, optional): Number of days for 'trend', 'last_n_days_average', or 'last_n_days_total' queries.

#     Returns:
#         str: SQL query string.

#     Raises:
#         ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend', 'last_n_days_average', or 'last_n_days_total'.
#     """
#     today = date.today()
#     yesterday = today - timedelta(days=1)
#     first_day_of_month = today.replace(day=1)

#     if metric_type == "today":
#         sql = f"""
#             SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date = '{today}';
#         """

#     elif metric_type == "yesterday":
#         sql = f"""
#             SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date = '{yesterday}';
#         """

#     elif metric_type == "monthly_total":
#         sql = f"""
#             SELECT plant_long_name, SUM(edit_generation) AS MONTHLY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date BETWEEN '{first_day_of_month}' AND '{today}'
#             GROUP BY plant_long_name;
#         """

#     elif metric_type == "ytd_total":
#         sql = f"""
#             SELECT plant_long_name, SUM(edit_generation) AS YTD_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND YEAR(date) = {today.year}
#             GROUP BY plant_long_name;
#         """

#     elif metric_type == "lifetime_total":
#         sql = f"""
#             SELECT plant_long_name, SUM(edit_generation) AS LIFETIME_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#             GROUP BY plant_long_name;
#         """

#     elif metric_type == "monthly_average":
#         sql = f"""
#             SELECT plant_long_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date BETWEEN '{first_day_of_month}' AND '{today}'
#             GROUP BY plant_long_name;
#         """

#     elif metric_type == "highest_day_month":
#         sql = f"""
#             SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND MONTH(date) = {today.month}
#               AND YEAR(date) = {today.year}
#             ORDER BY edit_generation DESC
#             LIMIT 1;
#         """

#     elif metric_type == "lowest_day_month":
#         sql = f"""
#             SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND MONTH(date) = {today.month}
#               AND YEAR(date) = {today.year}
#             ORDER BY edit_generation ASC
#             LIMIT 1;
#         """

#     elif metric_type == "trend" and days:
#         start_date = today - timedelta(days=days)
#         sql = f"""
#             SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date BETWEEN '{start_date}' AND '{today}'
#             ORDER BY date ASC;
#         """

#     elif metric_type == "last_n_days_average" and days:
#         start_date = today - timedelta(days=days)
#         sql = f"""
#             SELECT plant_long_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date BETWEEN '{start_date}' AND '{today}'
#             GROUP BY plant_long_name;
#         """

#     elif metric_type == "last_n_days_total" and days:
#         start_date = today - timedelta(days=days)
#         sql = f"""
#             SELECT plant_long_name, SUM(edit_generation) AS TOTAL_GENERATION_LAST_{days}_DAYS
#             FROM dgr_solar_db
#             WHERE plant_short_name = '{plant_id}'
#               AND date BETWEEN '{start_date}' AND '{today}'
#             GROUP BY plant_long_name;
#         """

#     else:
#         raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average/last_n_days_total")

#     return sql.strip()

from datetime import date, timedelta
from mcp import tool

@tool(
    name="create_sql_query_generation_solar",
    description=(
        "Generate SQL queries to fetch solar plant generation metrics from the DGR database "
        "using the 'edit_generation' column. Supported metric types include: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Sum of generation for the current month "
        "- 'monthly_average': Average daily generation for the current month "
        "- 'highest_day_month': Day with the highest generation this month "
        "- 'lowest_day_month': Day with the lowest generation this month "
        "- 'ytd_total': Year-to-date total generation (Jan 1 to today) "
        "- 'lifetime_total': Total generation across all records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter) "
        "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter) "
        "- 'custom_range': Total generation between a given start_date and end_date (requires both parameters)."
    )
)
def create_sql_query_generation_solar(
    plant_id: str,
    metric_type: str,
    days: int = None,
    start_date: str = None,
    end_date: str = None
) -> str:
    """
    Generate SQL queries to retrieve solar plant generation metrics from the DGR database.

    This MCP tool dynamically constructs SQL queries based on the specified `metric_type`
    and optional parameters such as `days`, `start_date`, and `end_date`. It supports 
    predefined period-based metrics (e.g., daily, monthly, YTD) and flexible user-defined
    ranges for ad-hoc analysis.

    Supported metric types:
        - 'today': Fetch generation for the current date.
        - 'yesterday': Fetch generation for the previous date.
        - 'monthly_total': Compute total generation for the current month.
        - 'monthly_average': Compute average daily generation for the current month.
        - 'highest_day_month': Identify the day with the highest generation this month.
        - 'lowest_day_month': Identify the day with the lowest generation this month.
        - 'ytd_total': Compute total generation from Jan 1 of the current year to today.
        - 'lifetime_total': Compute total generation across all records.
        - 'trend': Return daily generation for the last N days (requires `days`).
        - 'last_n_days_average': Compute average generation for the last N days (requires `days`).
        - 'last_n_days_total': Compute total generation for the last N days (requires `days`).
        - 'custom_range': Compute total generation between `start_date` and `end_date`.

    Args:
        plant_id (str):
            Short name of the solar plant (e.g., "IN.INTE.KIDS").
            All plant IDs follow the convention prefix 'IN.INTE.'.
        metric_type (str):
            The type of metric or aggregation to query.
            Must be one of the supported metric types listed above.
        days (int, optional):
            Number of days to consider for metrics such as 'trend',
            'last_n_days_average', or 'last_n_days_total'.
            Required for those metric types only.
        start_date (str, optional):
            Start date (inclusive) for custom date range queries.
            Required if `metric_type` is 'custom_range'. Format: 'YYYY-MM-DD'.
        end_date (str, optional):
            End date (inclusive) for custom date range queries.
            Required if `metric_type` is 'custom_range'. Format: 'YYYY-MM-DD'.

    Returns:
        str:
            A fully constructed, ready-to-execute SQL query string targeting the
            `dgr_solar_db` table. The query is designed for read-only analytical
            purposes and follows strict filtering by `plant_short_name`.

    Raises:
        ValueError:
            If `metric_type` is not recognized, or if a required parameter
            (`days`, `start_date`, or `end_date`) is missing for its corresponding type.

    Example:
        >>> create_sql_query_generation_solar(
        ...     plant_id="IN.INTE.KIDS",
        ...     metric_type="custom_range",
        ...     start_date="2025-10-01",
        ...     end_date="2025-10-15"
        ... )
        "SELECT plant_long_name, SUM(edit_generation) AS TOTAL_GENERATION_CUSTOM_RANGE
        FROM dgr_solar_db
        WHERE plant_short_name = 'IN.INTE.KIDS'
        AND date BETWEEN '2025-10-01' AND '2025-10-15'
        GROUP BY plant_long_name;"
    """

    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    # --- PREDEFINED METRIC TYPES ---
    if metric_type == "today":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_total":
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_long_name;
        """

    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_long_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_long_name;
        """

    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND MONTH(date) = {today.month}
              AND YEAR(date) = {today.year}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND MONTH(date) = {today.month}
              AND YEAR(date) = {today.year}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """

    elif metric_type == "ytd_total":
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS YTD_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND YEAR(date) = {today.year}
            GROUP BY plant_long_name;
        """

    elif metric_type == "lifetime_total":
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS LIFETIME_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
            GROUP BY plant_long_name;
        """

    elif metric_type == "trend" and days:
        start_date_calc = today - timedelta(days=days)
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date_calc}' AND '{today}'
            ORDER BY date ASC;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date_calc = today - timedelta(days=days)
        sql = f"""
            SELECT plant_long_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date_calc}' AND '{today}'
            GROUP BY plant_long_name;
        """

    elif metric_type == "last_n_days_total" and days:
        start_date_calc = today - timedelta(days=days)
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS TOTAL_GENERATION_LAST_{days}_DAYS
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date_calc}' AND '{today}'
            GROUP BY plant_long_name;
        """

    # --- NEW: CUSTOM DATE RANGE METRIC ---
    elif metric_type == "custom_range" and start_date and end_date:
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS TOTAL_GENERATION_CUSTOM_RANGE
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{end_date}'
            GROUP BY plant_long_name;
        """

    else:
        raise ValueError(
            "Invalid metric_type or missing required parameters for trend/last_n_days/custom_range"
        )

    return sql.strip()



@mcp.tool(
    name="create_sql_query_pr_solar",
    description=(
        "Generate SQL queries to fetch solar plant Performance Ratio (PR%) metrics from the DGR database "
        "plant_id always start from IN.INTE."
        "using the 'pr' column. Supported metric types include: "
        "- 'today': Average PR% for the current date "
        "- 'yesterday': Average PR% for the previous date "
        "- 'monthly_average': Average PR% from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest average PR% in the current month "
        "- 'lowest_day_month': Day with the lowest average PR% in the current month "
        "- 'ytd_average': Year-to-date average PR% (Jan 1st of this year up to today) "
        "- 'lifetime_average': Average PR% across all available records "
        "- 'trend': Daily average PR% trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average PR% for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_pr_solar(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch solar plant Performance Ratio (PR%) metrics from the DGR database.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
        SELECT plant_long_name, date, edit_pr AS PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
        SELECT plant_long_name, date, edit_pr AS PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
        SELECT plant_long_name, AVG(edit_pr) AS MONTHLY_AVG_PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{first_day_of_month}' AND '{today}'
        GROUP BY plant_long_name;
        """

    elif metric_type == "lifetime_average":
        return f"""
        SELECT plant_long_name, AVG(edit_pr) AS LIFETIME_AVG_PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
        GROUP BY plant_long_name;
        """

    elif metric_type == "trend":
        if not days:
            days = 7
        start_date = today - timedelta(days=days)
        return f"""
        SELECT plant_long_name, date, edit_pr AS PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{start_date}' AND '{today}'
        ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
        SELECT plant_long_name, AVG(edit_pr) AS AVG_PR_LAST_{days}_DAYS
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{start_date}' AND '{today}'
        GROUP BY plant_long_name;
        """

    elif metric_type == "compare_months":
        return f"""
        SELECT DATE_FORMAT(date, '%Y-%m-01') AS month_start,
               AVG(edit_pr) AS avg_pr
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND DATE_FORMAT(date, '%Y-%m') IN (
              DATE_FORMAT('{today}', '%Y-%m'),
              DATE_FORMAT(DATE_SUB('{today}', INTERVAL 1 MONTH), '%Y-%m')
          )
        GROUP BY month_start
        ORDER BY month_start;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend")


@mcp.tool(
    name="create_sql_query_poa_solar",
    description=(
        "Generate SQL queries to fetch solar plant Plane of Array (POA, irradiance) metrics "
        "plant_id always start from IN.INTE."
        "from the DGR database using the 'poa' column. Supported metric types include: "
        "- 'today': POA for the current date "
        "- 'yesterday': POA for the previous date "
        "- 'monthly_average': Average daily POA from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest POA in the current month "
        "- 'lowest_day_month': Day with the lowest POA in the current month "
        "- 'daily_average_month': Daily average POA values across the current month "
        "- 'trend': Daily POA trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average daily POA for the last N days (requires 'days' parameter). "
        "- 'compare_last_month': Comparison of average POA for the current month vs the previous month."
    )
)
def create_sql_query_poa_solar(plant_id: str, metric_type: str, days: int = None) -> str:
    if metric_type == "today":
        return f"""
        SELECT plant_long_name, date, (poa / 1000) AS poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = CURRENT_DATE;
        """

    elif metric_type == "yesterday":
        return f"""
        SELECT plant_long_name, date, (poa / 1000) AS poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = CURRENT_DATE - INTERVAL 1 DAY;
        """

    elif metric_type == "monthly_average":
        return f"""
        SELECT plant_long_name, AVG(poa / 1000) AS monthly_average_poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
        GROUP BY plant_long_name;
        """

    elif metric_type == "highest_day_month":
        return f"""
        SELECT plant_long_name, date, (poa / 1000) AS poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
        ORDER BY poa DESC
        LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
        SELECT plant_long_name, date, (poa / 1000) AS poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
        ORDER BY poa ASC
        LIMIT 1;
        """

    elif metric_type == "daily_average_month":
        return f"""
        SELECT plant_long_name, date, AVG(poa / 1000) AS daily_average_poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
        GROUP BY plant_long_name, date
        ORDER BY date;
        """

    elif metric_type == "trend":
        if not days:
            raise ValueError("The 'days' parameter is required for the 'trend' metric_type.")
        return f"""
        SELECT plant_long_name, date, (poa / 1000) AS poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date >= CURRENT_DATE - INTERVAL {days} DAY
        ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        return f"""
        SELECT plant_long_name, AVG(poa / 1000) AS AVG_POA_LAST_{days}_DAYS
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN CURRENT_DATE - INTERVAL {days} DAY AND CURRENT_DATE
        GROUP BY plant_long_name;
        """

    elif metric_type == "compare_last_month":
        return f"""
        SELECT 
            AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                     THEN poa / 1000 END) AS current_month_poa,
            AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m')
                     THEN poa / 1000 END) AS last_month_poa
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}';
        """

    else:
        raise ValueError(f"Unsupported metric_type: {metric_type}")




# ===============================
# WIND GENERATION TOOL
# ===============================



@mcp.tool(
    name="create_sql_query_generation_wind",
    description=(
        "Generate SQL queries to fetch wind plant generation metrics from the DGR database "
        "plant_id always start from IN.INTE."
        "using the 'edit_generation' column. Supported metric types include: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Sum of generation from the first day of the current month up to today it only the current month not previous month"
        "- 'monthly_average': Average daily generation this month "
        "- 'highest_day_month': Day with the highest generation this month "
        "- 'lowest_day_month': Day with the lowest generation this month "
        "- 'ytd_total': Year-to-date total generation (Jan 1st up to today) "
        "- 'lifetime_total': Total generation across all available records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter). "
        "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_generation_wind(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch wind plant generation metrics from the DGR database.

    Args:
        plant_id (str): Short name of the wind plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of generation metric to fetch. Supported values:
            - 'today': Daily generation for the current date
            - 'yesterday': Daily generation for the previous date
            - 'monthly_total': Sum of generation from the first day of the current month up to today it only the current month not previous month
            - 'monthly_average': Average daily generation this month
            - 'highest_day_month': Day with the highest generation this month
            - 'lowest_day_month': Day with the lowest generation this month
            - 'ytd_total': Year-to-date total generation (Jan 1st up to today)
            - 'lifetime_total': Total generation across all available records
            - 'trend': Daily generation trend for the last N days (requires 'days' parameter)
            - 'last_n_days_average': Average generation for the last N days (requires 'days' parameter)
            - 'last_n_days_total': Total generation for the last N days (requires 'days' parameter)
        days (int, optional): Number of days for 'trend', 'last_n_days_average', or 'last_n_days_total' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend', 'last_n_days_average', or 'last_n_days_total'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT plant_short_name, plant_long_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """

    elif metric_type == "ytd_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS YTD_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(YEAR FROM date) = {today.year}
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "lifetime_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS LIFETIME_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_short_name, plant_long_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "last_n_days_total" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS TOTAL_GENERATION_LAST_{days}_DAYS
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend/average/total")
    



# ===============================
# WIND SPEED TOOL
# ===============================
@mcp.tool(
    name="create_sql_query_wind_speed",
    description=(
        "Generate SQL queries to fetch wind plant Wind Speed metrics from the DGR database "
        "plant_id always start from IN.INTE"
        "using the 'edit_wind_speed' column. Supported metric types include: "
        "- 'today': Average wind speed for the current date "
        "- 'yesterday': Average wind speed for the previous date "
        "- 'monthly_average': Average wind speed from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest wind speed this month "
        "- 'lowest_day_month': Day with the lowest wind speed this month "
        "- 'lifetime_average': Lifetime average wind speed "
        "- 'trend': Daily wind speed trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average wind speed for the last N days (requires 'days' parameter). "
        "- 'compare_months': Compare average wind speed of this month vs last month."
    )
)
def create_sql_query_wind_speed(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch wind plant wind speed metrics from the DGR database.

    Args:
        plant_id (str): Short name of the wind plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of wind speed metric to fetch. Supported values:
            - 'today': Average wind speed for the current date
            - 'yesterday': Average wind speed for the previous date
            - 'monthly_average': Average wind speed from the first day of the current month up to today
            - 'highest_day_month': Day with the highest wind speed this month
            - 'lowest_day_month': Day with the lowest wind speed this month
            - 'lifetime_average': Lifetime average wind speed
            - 'trend': Daily wind speed trend for the last N days (requires 'days' parameter)
            - 'compare_months': Compare average wind speed of this month vs last month
        days (int, optional): Number of days for 'trend' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS MONTHLY_AVG_WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}';
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed ASC
            LIMIT 1;
        """

    elif metric_type == "lifetime_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS LIFETIME_AVG_WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}';
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT AVG(edit_wind_speed) AS AVG_WIND_SPEED_LAST_{days}_DAYS
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
              AND date <= '{today}';
        """

    elif metric_type == "compare_months":
        return f"""
            WITH monthly_ws AS (
                SELECT DATE_TRUNC('month', date) AS month,
                       AVG(edit_wind_speed) AS avg_ws
                FROM dgr_wind_db
                WHERE plant_short_name = '{plant_id}'
                GROUP BY DATE_TRUNC('month', date)
            )
            SELECT *
            FROM monthly_ws
            WHERE month IN (
                DATE_TRUNC('month', '{today}'),
                DATE_TRUNC('month', '{today}' - INTERVAL '1 month')
            )
            ORDER BY month;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend")



# @mcp.tool(
#     name="generate_sql_query_turbine_wise",
#     description=(
#         "Generate SQL queries to fetch turbine-wise wind speed and generation metrics "
#         "the plant_id is always start with IN.INTE."
#         "if user asked specfically for turbine then only use this function"
#         "from the dgr_wind_db (edit_csv_report_data column) for a given wind plant.\n"
#         "The column stores JSON arrays like: "
#         "[{\"Loc No\":\"T01\",\"Avg Wind Speed\":\"8.02\",\"Daily Generation (kWh)\":\"37606\"}, ...].\n"
#         "Both 'Avg Wind Speed' and 'Daily Generation (kWh)' are extracted together. "
#         "Empty or missing values are treated as NULLs in aggregation.\n\n"
#         "Supported metrics:\n"
#         "- 'today': Wind speed + generation for the current date\n"
#         "- 'yesterday': Wind speed + generation for the previous date\n"
#         "- 'monthly_average': Average wind speed + generation this month\n"
#         "- 'monthly_total': Total generation this month (wind speed averaged)\n"
#         "- 'highest_day_month': Day with the highest generation this month (with wind speed)\n"
#         "- 'lowest_day_month': Day with the lowest generation this month (with wind speed)\n"
#         "- 'ytd_total': Year-to-date total generation (with avg wind speed)\n"
#         "- 'lifetime_total': Lifetime generation + average wind speed\n"
#         "- 'trend': Daily wind speed + generation trend for the last N days (requires 'days' parameter)\n"
#         "- 'last_n_days_average': Average wind speed and generation per turbine for the last N days (requires 'days' parameter).\n"
#         "- 'compare_months': Compare average wind speed + generation of this month vs last month\n"
#         "- 'debug_raw': Raw expansion of JSON rows without aggregation (for debugging data ingestion)\n"
#     )
# )
# def generate_sql_query_turbine_wise(
#     plant_id: str,
#     metric: str,
#     days: int = None,
# ) -> str:
#     """
#     Generate MySQL SQL queries to fetch turbine-wise wind speed and generation metrics.

#     Args:
#         plant_id (str): Plant identifier (e.g., 'IN.INTE.KIDS').
#         metric (str): today, yesterday, monthly_average, monthly_total,
#                       highest_day_month, lowest_day_month, ytd_total,
#                       lifetime_total, trend, compare_months, debug_raw
#         days (int, optional): Number of days for 'trend' (default=7).

#     Returns:
#         str: SQL query string.
#     """
#     today = date.today()
#     yesterday = today - timedelta(days=1)
#     first_of_month = today.replace(day=1)
#     first_of_year = today.replace(month=1, day=1)

#     # JSON_TABLE for expansion
#     json_table = """
#         JSON_TABLE(
#             d.edit_csv_report_data, '$[*]'
#             COLUMNS(
#                 turbine_id VARCHAR(20) PATH '$."Loc No"',
#                 avg_ws DECIMAL(10,2) PATH '$."Avg Wind Speed"',
#                 gen_kwh DECIMAL(15,2) PATH '$."Daily Generation (kWh)"'
#             )
#         ) elem
#     """

#     if metric == "debug_raw":
#         return f"""
#             SELECT d.date, elem.turbine_id, elem.avg_ws, elem.gen_kwh
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date BETWEEN '{first_of_month}' AND '{today}'
#             ORDER BY d.date, elem.turbine_id;
#         """

#     if metric == "today":
#         return f"""
#             SELECT d.date, elem.turbine_id,
#                    AVG(elem.avg_ws) AS avg_wind_speed,
#                    SUM(elem.gen_kwh) AS daily_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}' AND d.date = '{today}'
#             GROUP BY d.date, elem.turbine_id;
#         """

#     elif metric == "yesterday":
#         return f"""
#             SELECT d.date, elem.turbine_id,
#                    AVG(elem.avg_ws) AS avg_wind_speed,
#                    SUM(elem.gen_kwh) AS daily_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}' AND d.date = '{yesterday}'
#             GROUP BY d.date, elem.turbine_id;
#         """

#     elif metric == "monthly_average":
#         return f"""
#             SELECT elem.turbine_id,
#                    AVG(elem.avg_ws) AS monthly_avg_wind_speed,
#                    AVG(elem.gen_kwh) AS monthly_avg_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date BETWEEN '{first_of_month}' AND '{today}'
#             GROUP BY elem.turbine_id;
#         """

#     elif metric == "monthly_total":
#         return f"""
#             SELECT elem.turbine_id,
#                    AVG(elem.avg_ws) AS monthly_avg_wind_speed,
#                    SUM(elem.gen_kwh) AS monthly_total_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date BETWEEN '{first_of_month}' AND '{today}'
#             GROUP BY elem.turbine_id;
#         """

#     elif metric == "highest_day_month":
#         return f"""
#             WITH daily AS (
#                 SELECT d.date, elem.turbine_id,
#                        AVG(elem.avg_ws) AS avg_wind_speed,
#                        SUM(elem.gen_kwh) AS daily_generation
#                 FROM dgr_wind_db d,
#                      {json_table}
#                 WHERE d.plant_short_name = '{plant_id}'
#                   AND d.date BETWEEN '{first_of_month}' AND '{today}'
#                 GROUP BY d.date, elem.turbine_id
#             )
#             SELECT d1.*
#             FROM daily d1
#             WHERE d1.daily_generation = (
#                 SELECT MAX(d2.daily_generation) FROM daily d2
#                 WHERE d2.turbine_id = d1.turbine_id
#             );
#         """

#     elif metric == "lowest_day_month":
#         return f"""
#             WITH daily AS (
#                 SELECT d.date, elem.turbine_id,
#                        AVG(elem.avg_ws) AS avg_wind_speed,
#                        SUM(elem.gen_kwh) AS daily_generation
#                 FROM dgr_wind_db d,
#                      {json_table}
#                 WHERE d.plant_short_name = '{plant_id}'
#                   AND d.date BETWEEN '{first_of_month}' AND '{today}'
#                 GROUP BY d.date, elem.turbine_id
#             )
#             SELECT d1.*
#             FROM daily d1
#             WHERE d1.daily_generation = (
#                 SELECT MIN(d2.daily_generation) FROM daily d2
#                 WHERE d2.turbine_id = d1.turbine_id
#             );
#         """

#     elif metric == "ytd_total":
#         return f"""
#             SELECT elem.turbine_id,
#                    AVG(elem.avg_ws) AS ytd_avg_wind_speed,
#                    SUM(elem.gen_kwh) AS ytd_total_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date BETWEEN '{first_of_year}' AND '{today}'
#             GROUP BY elem.turbine_id;
#         """

#     elif metric == "lifetime_total":
#         return f"""
#             SELECT elem.turbine_id,
#                    AVG(elem.avg_ws) AS lifetime_avg_wind_speed,
#                    SUM(elem.gen_kwh) AS lifetime_total_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#             GROUP BY elem.turbine_id;
#         """

#     elif metric == "trend":
#         return f"""
#             SELECT d.date, elem.turbine_id,
#                    AVG(elem.avg_ws) AS avg_wind_speed,
#                    SUM(elem.gen_kwh) AS daily_generation
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date BETWEEN '{today - timedelta(days=days or 7)}' AND '{today}'
#             GROUP BY d.date, elem.turbine_id
#             ORDER BY d.date, elem.turbine_id;
#         """

#     elif metric == "last_n_days_average" and days:
#         start_date = today - timedelta(days=days)
#         return f"""
#             SELECT elem.turbine_id,
#                    AVG(elem.avg_ws) AS avg_wind_speed_last_{days}_days,
#                    AVG(elem.gen_kwh) AS avg_generation_last_{days}_days
#             FROM dgr_wind_db d,
#                  {json_table}
#             WHERE d.plant_short_name = '{plant_id}'
#               AND d.date >= '{start_date}'
#               AND d.date <= '{today}'
#             GROUP BY elem.turbine_id;
#         """

#     elif metric == "compare_months":
#         last_month_end = first_of_month - timedelta(days=1)
#         last_month_start = last_month_end.replace(day=1)

#         return f"""
#             WITH this_month AS (
#                 SELECT elem.turbine_id,
#                        AVG(elem.avg_ws) AS avg_ws,
#                        AVG(elem.gen_kwh) AS avg_gen
#                 FROM dgr_wind_db d,
#                      {json_table}
#                 WHERE d.plant_short_name = '{plant_id}'
#                   AND d.date BETWEEN '{first_of_month}' AND '{today}'
#                 GROUP BY elem.turbine_id
#             ),
#             last_month AS (
#                 SELECT elem.turbine_id,
#                        AVG(elem.avg_ws) AS avg_ws,
#                        AVG(elem.gen_kwh) AS avg_gen
#                 FROM dgr_wind_db d,
#                      {json_table}
#                 WHERE d.plant_short_name = '{plant_id}'
#                   AND d.date BETWEEN '{last_month_start}' AND '{last_month_end}'
#                 GROUP BY elem.turbine_id
#             )
#             SELECT COALESCE(t1.turbine_id, t2.turbine_id) AS turbine_id,
#                    t1.avg_ws AS this_month_avg_ws,
#                    t1.avg_gen AS this_month_avg_gen,
#                    t2.avg_ws AS last_month_avg_ws,
#                    t2.avg_gen AS last_month_avg_gen
#             FROM this_month t1
#             LEFT JOIN last_month t2 ON t1.turbine_id = t2.turbine_id;
#         """

#     else:
#         raise ValueError(f"Invalid metric: {metric}")





# ===============================
# HYBRID PLANT TOOLS
# ===============================

@mcp.tool(
    name="create_sql_query_generation_hybrid",
    description=(
        "Generate SQL queries to fetch hybrid plant generation metrics from the DGR database (dgr_both_db table). "
        "You must specify the 'source' parameter as 'solar', 'wind', or 'both'. "
        "Supported metric types include: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Sum of generation from the first day of the current month up to today "
        "- 'monthly_average': Average daily generation from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest generation in the current month "
        "- 'lowest_day_month': Day with the lowest generation in the current month "
        "- 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today) "
        "- 'lifetime_total': Total generation across all available records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter). "
        "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_generation_hybrid(
    plant_id: str,
    source: str,
    metric_type: str,
    days: int = None
) -> str:
    """
    Generate SQL queries to fetch hybrid plant generation metrics from the DGR database (dgr_both_db).

    Args:
        plant_id (str): Short name of the plant (e.g., 'IN.INTE.KIDS').
        source (str): 'solar', 'wind', or 'both' (which part of the hybrid plant to query).
        metric_type (str): Type of generation metric to fetch. Supported values:
            - 'today', 'yesterday', 'monthly_total', 'monthly_average', 'highest_day_month', 'lowest_day_month',
              'ytd_total', 'lifetime_total', 'trend', 'last_n_days_average', 'last_n_days_total'
        days (int, optional): Number of days for 'trend', 'last_n_days_average', or 'last_n_days_total' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type or source is provided.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)
    first_day_of_year = today.replace(month=1, day=1)

    # Map source to columns
    if source == "solar":
        short_name_col = "plant_short_name_solar"
        long_name_col = "plant_long_name_solar"
        gen_col = "edit_generation_solar"
    elif source == "wind":
        short_name_col = "plant_short_name_wind"
        long_name_col = "plant_long_name_wind"
        gen_col = "edit_generation_wind"
    elif source == "both":
        # For 'both', sum both columns
        short_name_col = None
        long_name_col = None
        gen_col = None
    else:
        raise ValueError("Invalid source: must be 'solar', 'wind', or 'both'")

    if source in ("solar", "wind"):
        if metric_type == "today":
            return f"""
                SELECT {long_name_col}, date, {gen_col} AS DAILY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date = '{today}';
            """
        elif metric_type == "yesterday":
            return f"""
                SELECT {long_name_col}, date, {gen_col} AS DAILY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date = '{yesterday}';
            """
        elif metric_type == "monthly_total":
            return f"""
                SELECT {long_name_col}, SUM({gen_col}) AS MONTHLY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date >= '{first_day_of_month}'
                  AND date <= '{today}';
            """
        elif metric_type == "monthly_average":
            return f"""
                SELECT {long_name_col}, AVG({gen_col}) AS AVG_DAILY_GENERATION_THIS_MONTH
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date >= '{first_day_of_month}'
                  AND date <= '{today}';
            """
        elif metric_type == "highest_day_month":
            return f"""
                SELECT {long_name_col}, date, {gen_col} AS DAILY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND EXTRACT(MONTH FROM date) = {today.month}
                  AND EXTRACT(YEAR FROM date) = {today.year}
                ORDER BY {gen_col} DESC
                LIMIT 1;
            """
        elif metric_type == "lowest_day_month":
            return f"""
                SELECT {long_name_col}, date, {gen_col} AS DAILY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND EXTRACT(MONTH FROM date) = {today.month}
                  AND EXTRACT(YEAR FROM date) = {today.year}
                ORDER BY {gen_col} ASC
                LIMIT 1;
            """
        elif metric_type == "ytd_total":
            return f"""
                SELECT {long_name_col}, SUM({gen_col}) AS YTD_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND EXTRACT(YEAR FROM date) = {today.year};
            """
        elif metric_type == "lifetime_total":
            return f"""
                SELECT {long_name_col}, SUM({gen_col}) AS LIFETIME_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}';
            """
        elif metric_type == "trend" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT {long_name_col}, date, {gen_col} AS DAILY_GENERATION
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date >= '{start_date}'
                ORDER BY date ASC;
            """
        elif metric_type == "last_n_days_average" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT {long_name_col}, AVG({gen_col}) AS AVG_GENERATION_LAST_{days}_DAYS
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date >= '{start_date}'
                  AND date <= '{today}';
            """
        elif metric_type == "last_n_days_total" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT {long_name_col}, SUM({gen_col}) AS TOTAL_GENERATION_LAST_{days}_DAYS
                FROM dgr_both_db
                WHERE {short_name_col} = '{plant_id}'
                  AND date >= '{start_date}'
                  AND date <= '{today}';
            """
        else:
            raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average/last_n_days_total")
    elif source == "both":
        # For 'both', sum both solar and wind generation columns
        if metric_type == "today":
            return f"""
                SELECT date, 
                       plant_long_name_solar, plant_long_name_wind,
                       edit_generation_solar AS SOLAR_GENERATION,
                       edit_generation_wind AS WIND_GENERATION,
                       (COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION
                FROM dgr_both_db
                WHERE plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}'
                  AND date = '{today}';
            """
        elif metric_type == "yesterday":
            return f"""
                SELECT date, 
                       plant_long_name_solar, plant_long_name_wind,
                       edit_generation_solar AS SOLAR_GENERATION,
                       edit_generation_wind AS WIND_GENERATION,
                       (COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION
                FROM dgr_both_db
                WHERE plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}'
                  AND date = '{yesterday}';
            """
        elif metric_type == "monthly_total":
            return f"""
                SELECT 
                    SUM(edit_generation_solar) AS MONTHLY_SOLAR_GENERATION,
                    SUM(edit_generation_wind) AS MONTHLY_WIND_GENERATION,
                    SUM(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS MONTHLY_TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND date >= '{first_day_of_month}'
                  AND date <= '{today}';
            """
        elif metric_type == "monthly_average":
            return f"""
                SELECT 
                    AVG(edit_generation_solar) AS AVG_DAILY_SOLAR_GENERATION_THIS_MONTH,
                    AVG(edit_generation_wind) AS AVG_DAILY_WIND_GENERATION_THIS_MONTH,
                    AVG(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS AVG_DAILY_TOTAL_GENERATION_THIS_MONTH
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND date >= '{first_day_of_month}'
                  AND date <= '{today}';
            """
        elif metric_type == "highest_day_month":
            return f"""
                SELECT date, 
                       plant_long_name_solar, plant_long_name_wind,
                       edit_generation_solar AS SOLAR_GENERATION,
                       edit_generation_wind AS WIND_GENERATION,
                       (COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND EXTRACT(MONTH FROM date) = {today.month}
                  AND EXTRACT(YEAR FROM date) = {today.year}
                ORDER BY TOTAL_GENERATION DESC
                LIMIT 1;
            """
        elif metric_type == "lowest_day_month":
            return f"""
                SELECT date, 
                       plant_long_name_solar, plant_long_name_wind,
                       edit_generation_solar AS SOLAR_GENERATION,
                       edit_generation_wind AS WIND_GENERATION,
                       (COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND EXTRACT(MONTH FROM date) = {today.month}
                  AND EXTRACT(YEAR FROM date) = {today.year}
                ORDER BY TOTAL_GENERATION ASC
                LIMIT 1;
            """
        elif metric_type == "ytd_total":
            return f"""
                SELECT 
                    SUM(edit_generation_solar) AS YTD_SOLAR_GENERATION,
                    SUM(edit_generation_wind) AS YTD_WIND_GENERATION,
                    SUM(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS YTD_TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND EXTRACT(YEAR FROM date) = {today.year};
            """
        elif metric_type == "lifetime_total":
            return f"""
                SELECT 
                    SUM(edit_generation_solar) AS LIFETIME_SOLAR_GENERATION,
                    SUM(edit_generation_wind) AS LIFETIME_WIND_GENERATION,
                    SUM(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS LIFETIME_TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}');
            """
        elif metric_type == "trend" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT date, 
                       plant_long_name_solar, plant_long_name_wind,
                       edit_generation_solar AS SOLAR_GENERATION,
                       edit_generation_wind AS WIND_GENERATION,
                       (COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND date >= '{start_date}'
                ORDER BY date ASC;
            """
        elif metric_type == "last_n_days_average" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT 
                    AVG(edit_generation_solar) AS AVG_SOLAR_GENERATION_LAST_{days}_DAYS,
                    AVG(edit_generation_wind) AS AVG_WIND_GENERATION_LAST_{days}_DAYS,
                    AVG(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS AVG_TOTAL_GENERATION_LAST_{days}_DAYS
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND date >= '{start_date}'
                  AND date <= '{today}';
            """
        elif metric_type == "last_n_days_total" and days:
            start_date = today - timedelta(days=days)
            return f"""
                SELECT 
                    SUM(edit_generation_solar) AS TOTAL_SOLAR_GENERATION_LAST_{days}_DAYS,
                    SUM(edit_generation_wind) AS TOTAL_WIND_GENERATION_LAST_{days}_DAYS,
                    SUM(COALESCE(edit_generation_solar,0) + COALESCE(edit_generation_wind,0)) AS TOTAL_GENERATION_LAST_{days}_DAYS
                FROM dgr_both_db
                WHERE (plant_short_name_solar = '{plant_id}' OR plant_short_name_wind = '{plant_id}')
                  AND date >= '{start_date}'
                  AND date <= '{today}';
            """
        else:
            raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average/last_n_days_total")
    else:
        raise ValueError("Invalid source: must be 'solar', 'wind', or 'both'")

# ===============================
# HYBRID PR, POA, WIND SPEED TOOLS
# ===============================

@mcp.tool(
    name="create_sql_query_pr_hybrid",
    description=(
        "Generate SQL queries to fetch hybrid plant Performance Ratio (PR%) metrics from the DGR database (dgr_both_db table). "
        "This tool only supports the solar part of the hybrid plant. "
        "plant_id always start from IN.INTE."
        "Supported metric types include: "
        "- 'today': Average PR% for the current date "
        "- 'yesterday': Average PR% for the previous date "
        "- 'monthly_average': Average PR% from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest average PR% in the current month "
        "- 'lowest_day_month': Day with the lowest average PR% in the current month "
        "- 'ytd_average': Year-to-date average PR% (Jan 1st of this year up to today) "
        "- 'lifetime_average': Average PR% across all available records "
        "- 'trend': Daily average PR% trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average PR% for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_pr_hybrid(
    plant_id: str,
    metric_type: str,
    days: int = None
) -> str:
    """
    Generate SQL queries to fetch hybrid plant Performance Ratio (PR%) metrics from the DGR database (dgr_both_db).

    Args:
        plant_id (str): Short name of the solar part of the hybrid plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of PR metric to fetch. Supported values:
            - 'today', 'yesterday', 'monthly_average', 'highest_day_month', 'lowest_day_month',
              'ytd_average', 'lifetime_average', 'trend', 'last_n_days_average'
        days (int, optional): Number of days for 'trend' or 'last_n_days_average' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)
    first_day_of_year = today.replace(month=1, day=1)

    if metric_type == "today":
        return f"""
            SELECT plant_long_name_solar, date, edit_pr AS PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT plant_long_name_solar, date, edit_pr AS PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT plant_long_name_solar, AVG(edit_pr) AS MONTHLY_AVG_PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_long_name_solar;
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT plant_long_name_solar, date, edit_pr AS PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_pr DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT plant_long_name_solar, date, edit_pr AS PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_pr ASC
            LIMIT 1;
        """

    elif metric_type == "ytd_average":
        return f"""
            SELECT plant_long_name_solar, AVG(edit_pr) AS YTD_AVG_PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(YEAR FROM date) = {today.year}
            GROUP BY plant_long_name_solar;
        """

    elif metric_type == "lifetime_average":
        return f"""
            SELECT plant_long_name_solar, AVG(edit_pr) AS LIFETIME_AVG_PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
            GROUP BY plant_long_name_solar;
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name_solar, date, edit_pr AS PR_PERCENT
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name_solar, AVG(edit_pr) AS AVG_PR_LAST_{days}_DAYS
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            GROUP BY plant_long_name_solar;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average")

@mcp.tool(
    name="create_sql_query_poa_hybrid",
    description=(
        "Generate SQL queries to fetch hybrid plant Plane of Array (POA, irradiance) metrics from the DGR database (dgr_both_db table). "
        "This tool only supports the solar part of the hybrid plant. "
        "plant_id always start from IN.INTE."
        "Supported metric types include: "
        "- 'today': POA for the current date "
        "- 'yesterday': POA for the previous date "
        "- 'monthly_average': Average daily POA from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest POA in the current month "
        "- 'lowest_day_month': Day with the lowest POA in the current month "
        "- 'daily_average_month': Daily average POA values across the current month "
        "- 'trend': Daily POA trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average daily POA for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_poa_hybrid(
    plant_id: str,
    metric_type: str,
    days: int = None
) -> str:
    """
    Generate SQL queries to fetch normalized POA (Plane of Array irradiance) metrics 
    for the solar part of a hybrid plant.

    All POA values are divided by 1000 for unit normalization (e.g., Wh/m² → kWh/m²).

    Args:
        plant_id (str): Short name of the solar part of the hybrid plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of POA metric to fetch. Supported values:
            - 'today', 'yesterday', 'monthly_average', 'highest_day_month', 'lowest_day_month',
              'daily_average_month', 'trend', 'last_n_days_average'
        days (int, optional): Number of days for 'trend' or 'last_n_days_average' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or missing days parameter.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT plant_long_name_solar, date, (edit_poa / 1000) AS POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT plant_long_name_solar, date, (edit_poa / 1000) AS POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT plant_long_name_solar, AVG(edit_poa / 1000) AS MONTHLY_AVG_POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_long_name_solar;
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT plant_long_name_solar, date, (edit_poa / 1000) AS POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_poa DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT plant_long_name_solar, date, (edit_poa / 1000) AS POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_poa ASC
            LIMIT 1;
        """

    elif metric_type == "daily_average_month":
        return f"""
            SELECT plant_long_name_solar, date, AVG(edit_poa / 1000) AS DAILY_AVG_POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            GROUP BY plant_long_name_solar, date
            ORDER BY date;
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name_solar, date, (edit_poa / 1000) AS POA
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name_solar, AVG(edit_poa / 1000) AS AVG_POA_LAST_{days}_DAYS
            FROM dgr_both_db
            WHERE plant_short_name_solar = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            GROUP BY plant_long_name_solar;
        """

    else:
        raise ValueError("Invalid metric_type or missing days parameter for trend/last_n_days_average")


@mcp.tool(
    name="create_sql_query_wind_speed_hybrid",
    description=(
        "Generate SQL queries to fetch hybrid plant Wind Speed metrics from the DGR database (dgr_both_db table). "
        "This tool only supports the wind part of the hybrid plant. "
        "Supported metric types include: "
        "- 'today': Average wind speed for the current date "
        "- 'yesterday': Average wind speed for the previous date "
        "- 'monthly_average': Average wind speed from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest wind speed in the current month "
        "- 'lowest_day_month': Day with the lowest wind speed in the current month "
        "- 'lifetime_average': Lifetime average wind speed "
        "- 'trend': Daily wind speed trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average wind speed for the last N days (requires 'days' parameter)."
    )
)
def create_sql_query_wind_speed_hybrid(
    plant_id: str,
    metric_type: str,
    days: int = None
) -> str:
    """
    Generate SQL queries to fetch wind speed metrics for the wind part of a hybrid plant.

    Args:
        plant_id (str): Short name of the wind part of the hybrid plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of wind speed metric to fetch. Supported values:
            - 'today', 'yesterday', 'monthly_average', 'highest_day_month', 'lowest_day_month',
              'lifetime_average', 'trend', 'last_n_days_average'
        days (int, optional): Number of days for 'trend' or 'last_n_days_average' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND date = '{today}';
        """
    elif metric_type == "yesterday":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND date = '{yesterday}';
        """
    elif metric_type == "monthly_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS MONTHLY_AVG_WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}';
        """
    elif metric_type == "highest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed ASC
            LIMIT 1;
        """
    elif metric_type == "lifetime_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS LIFETIME_AVG_WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}';
        """
    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """
    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT AVG(edit_wind_speed) AS AVG_WIND_SPEED_LAST_{days}_DAYS
            FROM dgr_both_db
            WHERE plant_short_name_wind = '{plant_id}'
              AND date >= '{start_date}'
              AND date <= '{today}';
        """
    else:
        raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average")


@mcp.tool(
    name="fetch_data_from_db",
    description="Executes a raw SQL query against the DGR database and returns the results as a JSON string. Use for advanced or custom data retrieval."
)
def fetch_data_from_db(sql_query: str) -> str:
    """
    Execute a raw SQL query against the database and return results as a JSON string.

    Args:
        sql_query (str): A valid SQL query string to execute against the database.

    Returns:
        str: Query results as a JSON string (list of records).

    Raises:
        RuntimeError: If the database query fails.
    """
    try:
        with engine.connect() as connection:
            df = pd.read_sql(text(sql_query), connection)
            # Convert DataFrame to JSON (records format keeps it list-of-dicts)
            return df.to_json(orient="records")
    except Exception as e:
        logger.error(f"Database query failed: {e}")
        raise RuntimeError(f"Database query failed: {e}")




@mcp.tool(
    name="create_excel_sent_file",
    description=(
        "Executes a SQL query, exports the result as an Excel file, and sends it to the specified mobile number via WhatsApp. "
        "this tool will not create a sql query use the proper tools that avalible "
        "Use only if the user explicitly requests an Excel file. The assistant must reply with a fixed template message confirming the file was sent, including the date range, plant type, and plant name."
    ),
)
def create_excel_sent_file(sql_query: str, mobile_number: str) -> dict:
    """
    Execute a SQL query, export the result as an Excel file, and send it to a mobile number.

    Args:
        sql_query (str): SQL query string to execute.
        mobile_number (str): Recipient's mobile number.

    Returns:
        dict: {"success": True, "message": "..."}
    """
    logger.info("create_excel_sent_file invoked with query: %s", sql_query)

    # --- Step 1: Fetch data from DB ---
    try:
        json_str = fetch_data_from_db(sql_query)
        records = json.loads(json_str)
        if not records:
            raise ValueError("Query returned no data.")
        df = pd.DataFrame(records)
    except Exception as e:
        logger.exception("Data fetch or parsing failed")
        return {"success": False, "message": f"Unable to fetch or parse results: {e}"}

    # --- Step 2: Normalize date/time columns ---
    for col in df.columns:
        if pd.api.types.is_integer_dtype(df[col]):
            if df[col].between(1e12, 2e13).any():
                try:
                    df[col] = pd.to_datetime(df[col], unit="ms").dt.strftime("%Y-%m-%d")
                except Exception:
                    pass  # leave as-is if conversion fails

    # --- Step 3: Generate file name ---
    plant = _extract_plant_name(sql_query) or "plant"
    metric = _extract_metric(sql_query) or "data"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{plant}_{metric}_{timestamp}.xlsx"
    file_path = os.path.join(EXPORT_DIR, filename)

    # --- Step 4: Save Excel ---
    try:
        df.to_excel(file_path, index=False)
        logger.info("Excel successfully written: %s", file_path)
    except Exception as e:
        logger.exception("Excel writing failed")
        return {"success": False, "message": f"Failed to write Excel: {e}"}

    # --- Step 5: Send Excel via WhatsApp ---
    try:
        media_id = upload_whatsapp_media(file_path)
        send_whatsapp_document_mcp("91" + mobile_number, media_id, filename)
        time.sleep(3)
        delete_whatsapp_media(media_id)
        logger.info("Excel sent to %s", mobile_number)
    except Exception as e:
        logger.exception("Failed sending Excel")
        return {"success": False, "message": f"Failed to send Excel: {e}"}

    return {"success": True, "message": "Excel file successfully sent."}



# @mcp.tool(
#     name="resolve_date",
#     description="Converts a natural language date query (e.g., 'yesterday', 'last week') into a YYYY-MM-DD date string. Use to resolve user date requests for queries."
# )
# def resolve_date(question: str) -> str:
#     now = datetime.now()
#     return f"""
# You are an AI date resolver tool.

# Based on the user's query, return the correct date in the format YYYY-MM-DD. 
# Today's date is {now.strftime('%Y-%m-%d')}.

# Rules:
# - Only return the resolved date string (e.g., 2025-05-06).
# - Do not include any text, explanations, or formatting around the date.
# - If the query is relative (e.g., "yesterday", "this month", "last week"), compute accordingly.
# - If ambiguous, resolve using best common interpretation.

# Examples:
# User: "What is yesterday's generation?"
# Assistant: 2025-05-06

# User: "What is this month's generation?"
# Assistant: 2025-05-01

# User: "Show data from last week"
# Assistant: 2025-04-30

# Now resolve for:
# User: "{question}"
# """




# @mcp.tool(
#     name="get_wind_alarm",
#     description=(
#         "Fetches all inactive alarms for a specified wind plant within a given date range. "
#         "plant_id always start from IN.INTE. "
#         "for the alarms you can use the plant_id to fetch the alarms. for wind/hybrid"
#         "if the plant is hybrid pass the plant_id of wind plant."
#         "Returns alarm name, controller, message, severity, and state. Use to diagnose wind plant issues or maintenance needs."
#     )
# )
# def get_alarm_wind(
#     plant_id: str,
#     start_date: str,
#     end_date: str
# ) -> List[Dict[str, str]]:
#     """
#     Fetches inactive alarm records for a specified plant between two dates,
#     focusing on key details to assist in identifying and resolving plant issues.

#     Args:
#         plant_name (str): The name of the plant. Must start with the prefix defined in PLANT_PREFIX.
#         start_date (str): Start date of the query range in 'YYYY-MM-DD' format.
#         end_date (str): End date of the query range in 'YYYY-MM-DD' format.

#     Returns:
#         List[Dict[str, str]]: A list of dictionaries, each containing:
#             - name: Alarm name (alarmname)
#             - controller: Controller name (controllername)
#             - message: Description of the alarm
#             - severity: Severity level of the alarm
#             - state: State of the alarm (expected to be "Inactive")

#     Raises:
#         ValueError: If the plant_name does not start with the required prefix,
#                     or if start_date is after end_date,
#                     or if date formats are incorrect.

#     Example:
#         get_alarm("PLANT_XYZ", "2025-05-01", "2025-05-31")
#     """
#     logger.info(f"Called get_alarm: {plant_id}, {start_date} to {end_date}")

#     # Validate plant name
#     if not plant_id.startswith(PLANT_PREFIX):
#         raise ValueError(f"plant_id must start with '{PLANT_PREFIX}'")

#     # Validate & parse dates
#     start = _validate_date_str(start_date)
#     end = _validate_date_str(end_date)
#     if start > end:
#         raise ValueError("start_date must be on or before end_date")

#     # Define columns to retrieve
#     cols = ["alarmname", "controllername", "message", "severity", "state"]

#     # Fetch alarm data
#     df: pd.DataFrame = integration.fetchDataV2(
#         plant_id,
#         "Alarm",
#         ["Plant Alarm"],
#         None,
#         start,
#         end
#     )
#     logger.info(f"Raw alarm dataframe:\n{df}")

#     if 'state' in df.columns:
#         # Filter to inactive alarms and select relevant columns
#         df_filtered = df[df["state"] == "Inactive"][cols]
    
#         return df_filtered.to_dict(orient="records")
    
#     return "THERE IS NO ISSUE FOUND"




# @mcp.tool(
#     name="get_alarm_solar",
#     description=(
#         "Fetches all inactive alarms for a specified solar plant within a given date range. "
#         "plant_id always start from IN.INTE. "
#         "for the alarms you can use the plant_id to fetch the alarms. for solar/hybrid"
#         "if the plant is hybrid pass the plant_id of solar plant."
#         "Returns alarm name, controller, message, severity, and state. Use to diagnose solar plant issues or maintenance needs."
#     )
# )
# def get_alarm_solar(
#     plant_id: str,
#     start_date: str,
#     end_date: str
# ) -> List[Dict[str, str]]:
#     """
#     Fetches inactive alarm records for a specified solar plant between two dates,
#     focusing on key details to assist in identifying and resolving plant issues.

#     Args:
#         plant_name (str): The name of the solar plant. Must start with the prefix defined in PLANT_PREFIX.
#         start_date (str): Start date of the query range in 'YYYY-MM-DD' format.
#         end_date (str): End date of the query range in 'YYYY-MM-DD' format.

#     Returns:
#         List[Dict[str, str]]: A list of dictionaries, each containing:
#             - name: Alarm name (alarmname)
#             - controller: Controller name (controllername)
#             - message: Description of the alarm
#             - severity: Severity level of the alarm
#             - state: State of the alarm (expected to be "Inactive")

#     Raises:
#         ValueError: If the plant_name does not start with the required prefix,
#                     or if start_date is after end_date,
#                     or if date formats are incorrect.

#     Example:
#         get_alarm_solar("PLANT_SOLAR_XYZ", "2025-05-01", "2025-05-31")
#     """
#     logger.info(f"Called get_alarm_solar: {plant_id}, {start_date} to {end_date}")

#     # Validate plant name
#     if not plant_id.startswith(PLANT_PREFIX):
#         raise ValueError(f"plant_id must start with '{PLANT_PREFIX}'")

#     # Validate & parse dates
#     start = _validate_date_str(start_date)
#     end = _validate_date_str(end_date)
#     if start > end:
#         raise ValueError("start_date must be on or before end_date")

#     # Define columns to retrieve
#     cols = ["alarmname", "controllername", "message", "severity", "state"]

#     # Fetch alarm data
#     df: pd.DataFrame = integration.fetchDataV2(
#         plant_id,
#         "Alarm",
#         ["Inverter Alarm"],
#         None,
#         start,
#         end
#     )
#     logger.info(f"Raw alarm dataframe:\n{df}")


#     if 'state' in df.columns:
#         # Filter to inactive alarms and select relevant columns
#         df_filtered = df[df["state"] == "Inactive"][cols]

#         return df_filtered.to_dict(orient="records")
    
#     return "THERE IS NO ISSUE FOUND"



@mcp.tool(
    name="plot_generation_metrics",
    description=(
        "Generates and sends a professional line plot of daily energy generation (in kWh) for one or more plants. "
        "Only use this function when the user explicitly asks for plots or graphs. "
        "Input data must be provided as a JSON string containing daily generation metrics, with each record including at least the fields: 'date', 'plant_long_name', and 'DAILY_GENERATION'. "
        "The plot is clearly labeled and sent to the specified WhatsApp number (without country code prefix). "
        "Use this tool to visually summarize and communicate generation performance trends over time."
        "\n\n"
        "Inputs:\n"
        "- json_input (str): JSON string with daily generation data for one or more plants.\n"
        "- incoming_number (str): WhatsApp mobile number (without country code prefix) to send the plot to."
    )
)
def plot_generation_metrics(json_input: str, incoming_number: str) -> dict:
    """
    Generate and send a daily generation line plot.

    This tool parses daily energy generation data (kWh) for one or more plants
    and creates a clean, labeled line plot showing generation trends over time.
    The generated plot is then sent to the provided WhatsApp number.

    Args:
        json_input (str): JSON string containing daily generation data. Each record must include:
            - `date` (str): The date in YYYY-MM-DD format.
            - `plant_long_name` (str): The full name of the plant.
            - `DAILY_GENERATION` (float): Daily generation in kWh.
        incoming_number (str): WhatsApp number (without country code) where the plot should be sent.

    Returns:
        dict: A status dictionary with information about the plot generation and delivery.
    """
    return _plot_metric_line(
        json_input=json_input,
        value_column="DAILY_GENERATION",
        title="Daily Generation (kWh)",
        y_label="Generation (kWh)",
        filename_prefix="generation",
        incoming_number=incoming_number
    )


@mcp.tool(
    name="plot_pr_metrics",
    description=(
        "Generates and sends a professional line plot of daily Performance Ratio (PR%) for one or more solar plants. "
        "Only use this function when the user explicitly asks for plots or graphs. "
        "Input data must be provided as a JSON string containing daily PR metrics, with each record including at least the fields: 'date', 'plant_long_name', and 'PR'. "
        "The plot includes a reference line at 75% PR to indicate the industry threshold, is clearly labeled, and is sent to the specified WhatsApp number. "
        "Use this tool to visualize PR trends and assess plant performance."
        "\n\n"
        "Inputs:\n"
        "- json_input (str): JSON string with daily PR data for one or more plants.\n"
        "- incoming_number (str): WhatsApp mobile number (without country code prefix) to send the plot to."
    )
)
def plot_pr_metrics(json_input: str, incoming_number: str) -> dict:
    """
    Generate and send a daily Performance Ratio (PR%) line plot.

    This tool visualizes the daily PR% trends for one or more solar plants.
    It includes a 75% reference line as an industry-standard performance threshold.
    The resulting plot is sent to the provided WhatsApp number.

    Args:
        json_input (str): JSON string containing daily PR data. Each record must include:
            - `date` (str): The date in YYYY-MM-DD format.
            - `plant_long_name` (str): The full name of the plant.
            - `PR` (float): Daily Performance Ratio (%).
        incoming_number (str): WhatsApp number (without country code) where the plot should be sent.

    Returns:
        dict: A status dictionary with information about the plot generation and delivery.
    """
    return _plot_metric_line(
        json_input=json_input,
        value_column="PR",
        title="Daily Performance Ratio (PR%)",
        y_label="PR (%)",
        filename_prefix="pr",
        reference_lines=[{"y": 75, "color": "red", "linestyle": "--", "label": "PR Threshold (75%)"}],
        incoming_number=incoming_number
    )


@mcp.tool(
    name="plot_poa_metrics",
    description=(
        "Generates and sends a professional line plot of daily Plane of Array (POA, in kWh/m²) for one or more solar plants. "
        "Only use this function when the user explicitly asks for plots or graphs. "
        "Input data must be provided as a JSON string containing daily POA metrics, with each record including at least the fields: 'date', 'plant_long_name', and 'POA'. "
        "The plot is clearly labeled and sent to the specified WhatsApp number. "
        "Use this tool to visualize solar irradiance (POA) trends over time."
        "\n\n"
        "Inputs:\n"
        "- json_input (str): JSON string with daily POA data for one or more plants.\n"
        "- incoming_number (str): WhatsApp mobile number (without country code prefix) to send the plot to."
    )
)
def plot_poa_metrics(json_input: str, incoming_number: str) -> dict:
    """
    Generate and send a daily POA (Plane of Array) line plot.

    This tool visualizes daily POA (kWh/m²) trends for one or more solar plants,
    helping track irradiance and correlate it with generation and PR. The plot is
    labeled clearly and sent to the provided WhatsApp number.

    Args:
        json_input (str): JSON string containing daily POA data. Each record must include:
            - `date` (str): The date in YYYY-MM-DD format.
            - `plant_long_name` (str): The full name of the plant.
            - `POA` (float): Daily Plane of Array value (kWh/m²).
        incoming_number (str): WhatsApp number (without country code) where the plot should be sent.

    Returns:
        dict: A status dictionary with information about the plot generation and delivery.
    """
    return _plot_metric_line(
        json_input=json_input,
        value_column="POA",
        title="Daily Plane of Array (POA)",
        y_label="POA (kWh/m²)",
        filename_prefix="poa",
        incoming_number=incoming_number
    )


@mcp.tool(
    name="plot_wind_speed_metrics",
    description=(
        "Generates and sends a professional line plot of daily average wind speed (in m/s) for one or more wind plants. "
        "Only use this function when the user explicitly asks for plots or graphs. "
        "Input data must be provided as a JSON string containing daily wind speed metrics, with each record including at least the fields: 'date', 'plant_long_name', and 'WIND_SPEED'. "
        "The plot includes reference lines for cut-in (3 m/s) and cut-out (25 m/s) wind speeds, is clearly labeled, and is sent to the specified WhatsApp number. "
        "Use this tool to visualize wind speed trends and operational thresholds for wind plants."
        "\n\n"
        "Inputs:\n"
        "- json_input (str): JSON string with daily wind speed data for one or more plants.\n"
        "- incoming_number (str): WhatsApp mobile number (without country code prefix) to send the plot to."
    )
)
def plot_wind_speed_metrics(json_input: str, incoming_number: str) -> dict:
    """
    Generate and send a daily average wind speed line plot.

    This tool visualizes daily wind speed trends for one or more wind plants.
    It includes horizontal reference lines for:
      - Cut-in speed at 3 m/s (green dashed line)
      - Cut-out speed at 25 m/s (red dashed line)
    The plot is clearly labeled and sent to the provided WhatsApp number.

    Args:
        json_input (str): JSON string containing daily wind speed data. Each record must include:
            - `date` (str): The date in YYYY-MM-DD format.
            - `plant_long_name` (str): The full name of the plant.
            - `WIND_SPEED` (float): Daily average wind speed in m/s.
        incoming_number (str): WhatsApp number (without country code) where the plot should be sent.

    Returns:
        dict: A status dictionary with information about the plot generation and delivery.
    """
    return _plot_metric_line(
        json_input=json_input,
        value_column="WIND_SPEED",
        title="Daily Average Wind Speed",
        y_label="Wind Speed (m/s)",
        filename_prefix="wind_speed",
        reference_lines=[
            {"y": 3, "color": "green", "linestyle": "--", "label": "Cut-in Speed (3 m/s)"},
            {"y": 25, "color": "red", "linestyle": "--", "label": "Cut-out Speed (25 m/s)"}
        ],
        incoming_number=incoming_number
    )


# --------------------------
# New Tools
# --------------------------


# @mcp.tool(
#     name="create_sql_query_alarm_data",
#     description=(
#         "Generate SQL queries to fetch alarm information for solar, wind, or hybrid plants from the plant_alarms table. "
#         "Supports various metrics like today's alarms, severity filters, trends, top frequent alarms, etc. "
#         "Supported metric_type values: "
#         "- 'today': All alarms for the current date "
#         "- 'yesterday': All alarms for the previous date "
#         "- 'last_n_days': All alarms for the last N days (requires 'days') "
#         "- 'critical_today': Critical severity alarms for today "
#         "- 'state_count': Count of alarms grouped by state (Raised, Cleared, etc.) for today "
#         "- 'severity_count': Count of alarms grouped by severity for today "
#         "- 'trend': Daily alarm count trend for the last N days "
#         "- 'top_frequent': Top 5 most frequent alarm names for the last N days "
#         "- 'longest_duration': Top 5 alarms with the longest duration today "
#         "- 'average_resolution_time': Average resolution duration in minutes for the last N days "
#     )
# )
# def create_sql_query_alarm_data(
#     plant_id: str,
#     metric_type: str,
#     days: int = None,
#     alarm_name: str = None,
#     controller_name: str = None,
#     severity: str = None,
#     state: str = None
# ) -> str:
#     """
#     Generate dynamic SQL queries for retrieving alarm information from the `plant_alarms` table.

#     This tool supports **Solar**, **Wind**, and **Hybrid** plants and can return alarm data
#     for different time ranges, severities, states, and patterns. It is designed to answer 
#     a wide range of user queries related to alarms, such as today's alarms, critical alarms, 
#     top frequent alarms, trends over time, and average resolution times.

#     Parameters
#     ----------
#     plant_id : str
#         Unique identifier of the plant (Solar/Wind/Hybrid) for which alarms are to be fetched.
#     metric_type : str
#         The type of alarm metric to fetch. Supported values:
#         - 'today' : All alarms for the current date
#         - 'yesterday' : All alarms for the previous date
#         - 'last_n_days' : All alarms for the last N days (requires `days`)
#         - 'critical_today' : Critical severity alarms for today
#         - 'state_count' : Count of alarms grouped by state (Raised, Cleared, etc.) for today
#         - 'severity_count' : Count of alarms grouped by severity for today
#         - 'trend' : Daily alarm count trend for the last N days (requires `days`)
#         - 'top_frequent' : Top 5 most frequent alarms for the last N days (requires `days`)
#         - 'longest_duration' : Top 5 alarms with the longest duration for today
#         - 'average_resolution_time' : Average resolution time (in minutes) for the last N days
#     days : int, optional
#         Number of days to look back for 'last_n_days', 'trend', 'top_frequent', or 'average_resolution_time' metrics.
#     alarm_name : str, optional
#         Specific alarm name to filter results.
#     controller_name : str, optional
#         Controller or device name to filter results.
#     severity : str, optional
#         Severity level to filter alarms (e.g., 'Critical', 'Major', 'Minor').
#     state : str, optional
#         State to filter alarms (e.g., 'Raised', 'Cleared').

#     Returns
#     -------
#     str
#         A SQL query string that can be executed against the database to fetch the requested alarm data.

#     Raises
#     ------
#     ValueError
#         If the `metric_type` is invalid or required parameters (like `days` for certain metrics) are missing.

#     Examples
#     --------
#     >>> create_sql_query_alarm_data("SOLAR_001", "today")
#     "SELECT * FROM plant_alarms WHERE plant_id = 'SOLAR_001' AND alarm_date = '2025-10-09' ORDER BY raised_time DESC;"

#     >>> create_sql_query_alarm_data("HYBRID_005", "top_frequent", days=7)
#     "SELECT alarm_name, COUNT(*) AS occurrence_count FROM plant_alarms WHERE plant_id = 'HYBRID_005' 
#      AND alarm_date BETWEEN '2025-10-02' AND '2025-10-09' GROUP BY alarm_name ORDER BY occurrence_count DESC LIMIT 5;"
#     """
#     today = date.today()
#     yesterday = today - timedelta(days=1)

#     # Common filter conditions
#     filters = [f"plant_id = '{plant_id}'"]

#     if alarm_name:
#         filters.append(f"alarm_name = '{alarm_name}'")
#     if controller_name:
#         filters.append(f"controller_name = '{controller_name}'")
#     if severity:
#         filters.append(f"severity = '{severity}'")
#     if state:
#         filters.append(f"state = '{state}'")

#     where_clause = " AND ".join(filters)

#     # === Metric-based SQL generation ===
#     if metric_type == "today":
#         # Use raised_time for date filtering (00:00:00 to 23:59:59)
#         start_dt = f"{today} 00:00:00"
#         end_dt = f"{today} 23:59:59"
#         return f"""
#         SELECT * 
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND raised_time >= '{start_dt}' AND raised_time <= '{end_dt}'
#         ORDER BY raised_time DESC;
#         """

#     elif metric_type == "yesterday":
#         # Use raised_time for date filtering (00:00:00 to 23:59:59)
#         start_dt = f"{yesterday} 00:00:00"
#         end_dt = f"{yesterday} 23:59:59"
#         return f"""
#         SELECT * 
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND raised_time >= '{start_dt}' AND raised_time <= '{end_dt}'
#         ORDER BY raised_time DESC;
#         """

#     elif metric_type == "last_n_days" and days:
#         # Use raised_time for date range filtering (00:00:00 of start_date to 23:59:59 of today)
#         start_date = today - timedelta(days=days)
#         start_dt = f"{start_date} 00:00:00"
#         end_dt = f"{today} 23:59:59"
#         return f"""
#         SELECT * 
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND raised_time >= '{start_dt}' AND raised_time <= '{end_dt}'
#         ORDER BY raised_time DESC;
#         """

#     elif metric_type == "critical_today":
#         return f"""
#         SELECT * 
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date = '{today}'
#           AND severity = 'Critical'
#         ORDER BY raised_time DESC;
#         """

#     elif metric_type == "state_count":
#         return f"""
#         SELECT state, COUNT(*) AS alarm_count
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date = '{today}'
#         GROUP BY state;
#         """

#     elif metric_type == "severity_count":
#         return f"""
#         SELECT severity, COUNT(*) AS alarm_count
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date = '{today}'
#         GROUP BY severity;
#         """

#     elif metric_type == "trend" and days:
#         start_date = today - timedelta(days=days)
#         return f"""
#         SELECT alarm_date, COUNT(*) AS daily_alarm_count
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date BETWEEN '{start_date}' AND '{today}'
#         GROUP BY alarm_date
#         ORDER BY alarm_date;
#         """

#     elif metric_type == "top_frequent" and days:
#         start_date = today - timedelta(days=days)
#         return f"""
#         SELECT alarm_name, COUNT(*) AS occurrence_count
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date BETWEEN '{start_date}' AND '{today}'
#         GROUP BY alarm_name
#         ORDER BY occurrence_count DESC
#         LIMIT 5;
#         """

#     elif metric_type == "longest_duration":
#         return f"""
#         SELECT alarm_name, controller_name, severity, state, duration_minutes
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date = '{today}'
#         ORDER BY duration_minutes DESC
#         LIMIT 5;
#         """

#     elif metric_type == "average_resolution_time" and days:
#         start_date = today - timedelta(days=days)
#         return f"""
#         SELECT AVG(duration_minutes) AS avg_resolution_minutes
#         FROM plant_alarms
#         WHERE {where_clause}
#           AND alarm_date BETWEEN '{start_date}' AND '{today}'
#           AND resolved_time IS NOT NULL;
#         """

#     else:
#         raise ValueError(f"Invalid metric_type '{metric_type}' or missing required params.")
    





@mcp.tool(
    name="create_sql_query_alarm_data",
    description=(
        "Generate SQL queries to fetch alarm information for solar, wind, or hybrid plants from the plant_alarms table. "
        "Supports various metrics like today's alarms, severity filters, trends, top frequent alarms, etc. "
        "Supported metric_type values: "
            """    plant_id : str
                Unique identifier of the plant (Solar/Wind/Hybrid) for which alarms are to be fetched.
            metric_type : str
                The type of metric or report to generate. Supported values:
                - 'today' : All alarms for the current date.
                - 'yesterday' : All alarms for the previous date.
                - 'last_n_days' : All alarms in the last N days (requires `days`).
                - 'date_range' : Alarms between `start_date` and `end_date`.
                - 'specific_date' : Alarms on a specific date (`start_date` required).
                - 'critical_today' : Alarms with severity 'Critical' for today.
                - 'state_count' : Count of alarms grouped by state for today.
                - 'severity_count' : Count of alarms grouped by severity for today.
                - 'trend' : Daily alarm count trend for the last N days (requires `days`).
                - 'top_frequent' : Top N most frequent alarms over the last N days (requires `days`).
                - 'longest_duration' : Top N alarms with the longest duration today.
                - 'average_resolution_time' : Average alarm resolution time (in minutes) over the last N days (requires `days`).
            days : int, optional
                Number of days to look back for metrics such as 'last_n_days', 'trend', 'top_frequent', or 'average_resolution_time'.
            start_date : str, optional
                Start date in 'YYYY-MM-DD' format for 'date_range' or 'specific_date' metrics.
            end_date : str, optional
                End date in 'YYYY-MM-DD' format for 'date_range' metric.
            alarm_name : str, optional
                Filter results to a specific alarm name.
            controller_name : str, optional
                Filter results to a specific controller or device.
            severity : str, optional
                Filter results by alarm severity (e.g., 'Critical', 'Major', 'Minor').
            state : str, optional
                Filter results by alarm state (e.g., 'Raised', 'Cleared').
            keyword : str, optional
                Search keyword in alarm name or message using partial matching.
            top_n : int, optional
                Number of top results to return for metrics like 'top_frequent' or 'longest_duration'. Default is 5."""
    )
)
def create_sql_query_alarm_data(
    plant_id: str,
    metric_type: str,
    days: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    alarm_name: Optional[str] = None,
    controller_name: Optional[str] = None,
    severity: Optional[str] = None,
    state: Optional[str] = None,
    keyword: Optional[str] = None,
    top_n: Optional[int] = 5
) -> str:

    """
        Generate a dynamic SQL query to retrieve alarm information for a given plant from the `plant_alarms` table.

        This function supports flexible, production-ready queries for a variety of alarm-related user requests, 
        including:

        1. Time-based queries:
        - Today, yesterday, last N days, specific date, or custom date range.

        2. Filtering:
        - By alarm name, controller/device name, severity (e.g., Critical, Major, Minor), state (e.g., Raised, Cleared),
            or keyword search in alarm name or message.

        3. Aggregations and trends:
        - Count by severity or state.
        - Daily trend of alarms over a period.
        - Top N alarms by frequency or longest duration.
        - Average resolution time over a period.

        Parameters
        ----------
        plant_id : str
            Unique identifier of the plant (Solar/Wind/Hybrid) for which alarms are to be fetched.
        metric_type : str
            The type of metric or report to generate. Supported values:
            - 'today' : All alarms for the current date.
            - 'yesterday' : All alarms for the previous date.
            - 'last_n_days' : All alarms in the last N days (requires `days`).
            - 'date_range' : Alarms between `start_date` and `end_date`.
            - 'specific_date' : Alarms on a specific date (`start_date` required).
            - 'critical_today' : Alarms with severity 'Critical' for today.
            - 'state_count' : Count of alarms grouped by state for today.
            - 'severity_count' : Count of alarms grouped by severity for today.
            - 'trend' : Daily alarm count trend for the last N days (requires `days`).
            - 'top_frequent' : Top N most frequent alarms over the last N days (requires `days`).
            - 'longest_duration' : Top N alarms with the longest duration today.
            - 'average_resolution_time' : Average alarm resolution time (in minutes) over the last N days (requires `days`).
        days : int, optional
            Number of days to look back for metrics such as 'last_n_days', 'trend', 'top_frequent', or 'average_resolution_time'.
        start_date : str, optional
            Start date in 'YYYY-MM-DD' format for 'date_range' or 'specific_date' metrics.
        end_date : str, optional
            End date in 'YYYY-MM-DD' format for 'date_range' metric.
        alarm_name : str, optional
            Filter results to a specific alarm name.
        controller_name : str, optional
            Filter results to a specific controller or device.
        severity : str, optional
            Filter results by alarm severity (e.g., 'Critical', 'Major', 'Minor').
        state : str, optional
            Filter results by alarm state (e.g., 'Raised', 'Cleared').
        keyword : str, optional
            Search keyword in alarm name or message using partial matching.
        top_n : int, optional
            Number of top results to return for metrics like 'top_frequent' or 'longest_duration'. Default is 5.

        Returns
        -------
        str
            A SQL query string that can be executed against the `plant_alarms` table to retrieve
            the requested alarm data.

        Raises
        ------
        ValueError
            If `metric_type` is invalid or required parameters (like `days` for certain metrics) are missing.

        Examples
        --------
        >>> create_sql_query_alarm_data("SOLAR_001", "today")
        "SELECT * FROM plant_alarms WHERE plant_id = 'SOLAR_001' AND DATE(raised_time) = '2025-10-13';"

        >>> create_sql_query_alarm_data("HYBRID_005", "top_frequent", days=7, top_n=5)
        "SELECT alarm_name, COUNT(*) AS occurrence_count 
        FROM plant_alarms 
        WHERE plant_id = 'HYBRID_005' AND DATE(raised_time) BETWEEN '2025-10-06' AND '2025-10-13' 
        GROUP BY alarm_name ORDER BY occurrence_count DESC LIMIT 5;"
        """

    today = date.today()
    yesterday = today - timedelta(days=1)

    # --- SELECT clause ---
    select_clause = "SELECT *"
    group_by_clause = ""
    order_by_clause = ""
    limit_clause = ""

    # --- WHERE clauses ---
    where_clauses = [f"plant_id = '{plant_id}'"]

    # Time filters
    if metric_type == "today":
        where_clauses.append(f"DATE(raised_time) = '{today}'")
    elif metric_type == "yesterday":
        where_clauses.append(f"DATE(raised_time) = '{yesterday}'")
    elif metric_type in ["last_n_days", "trend", "top_frequent", "average_resolution_time"] and days:
        start_dt = today - timedelta(days=days)
        where_clauses.append(f"DATE(raised_time) BETWEEN '{start_dt}' AND '{today}'")
    elif metric_type == "date_range" and start_date and end_date:
        where_clauses.append(f"DATE(raised_time) BETWEEN '{start_date}' AND '{end_date}'")
    elif metric_type == "specific_date" and start_date:
        where_clauses.append(f"DATE(raised_time) = '{start_date}'")

    # Attribute filters
    if alarm_name:
        where_clauses.append(f"alarm_name = '{alarm_name}'")
    if controller_name:
        where_clauses.append(f"controller_name = '{controller_name}'")
    if severity:
        where_clauses.append(f"severity = '{severity}'")
    if state:
        where_clauses.append(f"state = '{state}'")
    if keyword:
        where_clauses.append(f"(alarm_name LIKE '%{keyword}%' OR message LIKE '%{keyword}%')")

    # Metric-specific modifications
    if metric_type == "severity_count":
        select_clause = "SELECT severity, COUNT(*) AS alarm_count"
        group_by_clause = "GROUP BY severity"

    elif metric_type == "state_count":
        select_clause = "SELECT state, COUNT(*) AS alarm_count"
        group_by_clause = "GROUP BY state"

    elif metric_type == "trend":
        select_clause = "SELECT alarm_date, COUNT(*) AS daily_alarm_count"
        group_by_clause = "GROUP BY alarm_date"
        order_by_clause = "ORDER BY alarm_date"

    elif metric_type == "top_frequent":
        select_clause = "SELECT alarm_name, COUNT(*) AS occurrence_count"
        group_by_clause = "GROUP BY alarm_name"
        order_by_clause = "ORDER BY occurrence_count DESC"
        limit_clause = f"LIMIT {top_n}"

    elif metric_type == "longest_duration":
        order_by_clause = "ORDER BY duration_minutes DESC"
        limit_clause = f"LIMIT {top_n}"

    elif metric_type == "average_resolution_time":
        select_clause = "SELECT AVG(duration_minutes) AS avg_resolution_minutes"
        where_clauses.append("resolved_time IS NOT NULL")

    # Final assembly
    query = f"""
    {select_clause}
    FROM plant_alarms
    WHERE {' AND '.join(where_clauses)}
    {group_by_clause}
    {order_by_clause}
    {limit_clause};
    """
    return re.sub(r"\s+", " ", query).strip()



@mcp.tool(
    name="create_sql_query_generation_inverter",
    description=(
        "Generate SQL queries to fetch inverter-level solar generation metrics from the `solar_inverter_data` table. "
        "Supports a variety of metric types for flexible reporting and analytics, including: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Total generation for the current month "
        "- 'monthly_average': Average daily generation for the current month "
        "- 'highest_day_month': Day with the highest generation in the current month "
        "- 'lowest_day_month': Day with the lowest generation in the current month "
        "- 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today) "
        "- 'lifetime_total': Lifetime total generation across all available records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average generation for the last N days (requires 'days' parameter) "
        "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter) "
        "The 'plant_id' should be the short name of the plant (e.g., 'IN.INTE.KIDS'). "
        "The 'inverter_name' parameter is optional; if omitted, results are returned for all inverters in the plant. "
        "Use this tool to construct SQL queries for detailed inverter-level generation analysis, reporting, or dashboarding."
    )
)
def create_sql_query_generation_inverter(
    plant_id: str,
    inverter_name: str = None,
    metric_type: str = "today",
    days: int = None
) -> str:
    """
    Generate SQL queries for inverter-level generation metrics.

    This function constructs SQL queries dynamically to fetch various inverter-level
    metrics from the `solar_inverter_data` table. Metrics can include daily values,
    monthly aggregates, highest/lowest daily generation, year-to-date totals, lifetime
    totals, and trends over a specified number of days.

    Parameters
    ----------
    plant_id : str
        Unique identifier or short name of the plant (e.g., 'IN.INTE.KIDS').
    inverter_name : str, optional
        Specific inverter to filter results. If None, returns all inverters for the plant.
    metric_type : str, default "today"
        Metric to fetch. Supported values:
        - 'today': Daily generation for the current date
        - 'yesterday': Daily generation for the previous day
        - 'monthly_total': Total generation for the current month
        - 'monthly_average': Average daily generation for the current month
        - 'highest_day_month': Day with highest generation in the current month
        - 'lowest_day_month': Day with lowest generation in the current month
        - 'ytd_total': Year-to-date total generation
        - 'lifetime_total': Lifetime total generation
        - 'trend': Daily generation for the last `days` days (requires `days`)
        - 'last_n_days_average': Average generation for the last `days` days (requires `days`)
        - 'last_n_days_total': Total generation for the last `days` days (requires `days`)
    days : int, optional
        Number of days for trend or last_n_days metrics. Required if `metric_type` is
        'trend', 'last_n_days_average', or 'last_n_days_total'.

    Returns
    -------
    str
        The constructed SQL query string.

    Raises
    ------
    ValueError
        If `metric_type` is invalid or if `days` is missing for metrics that require it.

    Example
    -------
    >>> create_sql_query_generation_inverter(
    ...     plant_id="IN.INTE.KIDS", 
    ...     inverter_name="INV_1", 
    ...     metric_type="monthly_total"
    ... )
    "SELECT plant_name, inverter_name, SUM(edit_generation) AS MONTHLY_GENERATION FROM solar_inverter_data ..."
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)
    inverter_filter = f"AND inverter_name = '{inverter_name}'" if inverter_name else ""

    if metric_type == "today":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_generation AS DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = '{today}' {inverter_filter};
        """
    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_generation AS DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = '{yesterday}' {inverter_filter};
        """
    elif metric_type == "monthly_total":
        sql = f"""
            SELECT plant_name, inverter_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_generation) AS AVG_DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_generation AS DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND MONTH(date) = {today.month} AND YEAR(date) = {today.year} {inverter_filter}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_generation AS DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND MONTH(date) = {today.month} AND YEAR(date) = {today.year} {inverter_filter}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """
    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_generation AS DAILY_GENERATION
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}' {inverter_filter}
            ORDER BY date ASC;
        """
    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "last_n_days_total" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_name, inverter_name, SUM(edit_generation) AS TOTAL_GENERATION_LAST_{days}_DAYS
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    else:
        raise ValueError("Invalid metric_type or missing 'days' for trend/last_n_days metrics")

    return sql.strip()




@mcp.tool(
    name="create_sql_query_pr_inverter",
    description=(
        "Generate SQL queries to fetch inverter-level Performance Ratio (PR%) metrics from the `solar_inverter_data` table. "
        "Supports a variety of metric types for flexible reporting and analytics, including: "
        "- 'today': PR% for the current date "
        "- 'yesterday': PR% for the previous date "
        "- 'monthly_average': Average PR% for the current month "
        "- 'highest_day_month': Day with the highest PR% in the current month "
        "- 'lowest_day_month': Day with the lowest PR% in the current month "
        "- 'ytd_average': Year-to-date average PR% (Jan 1st of this year up to today) "
        "- 'lifetime_average': Lifetime average PR% across all available records "
        "- 'trend': Daily PR% trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average PR% for the last N days (requires 'days' parameter) "
        "The 'plant_id' should be the short name of the plant (e.g., 'IN.INTE.KIDS'). "
        "The 'inverter_name' parameter is optional; if omitted, results are returned for all inverters in the plant. "
        "Use this tool to construct SQL queries for detailed inverter-level PR analysis, reporting, or dashboarding."
    )
)
def create_sql_query_pr_inverter(
    plant_id: str,
    inverter_name: str = None,
    metric_type: str = "today",
    days: int = None
) -> str:
    """
    Generate SQL queries for inverter-level Performance Ratio (PR%) metrics.

    This function dynamically constructs SQL queries to fetch inverter-level PR%
    metrics from the `solar_inverter_data` table. Metrics can include daily values,
    monthly averages, highest/lowest daily PR, year-to-date averages, lifetime averages,
    and trends over a specified number of days.

    Parameters
    ----------
    plant_id : str
        Unique identifier or short name of the plant (e.g., 'IN.INTE.KIDS').
    inverter_name : str, optional
        Specific inverter to filter results. If None, returns data for all inverters.
    metric_type : str, default "today"
        Type of PR metric to fetch. Supported values:
        - 'today': PR% for the current date
        - 'yesterday': PR% for the previous day
        - 'monthly_average': Average PR% for the current month
        - 'highest_day_month': Day with highest PR% in the current month
        - 'lowest_day_month': Day with lowest PR% in the current month
        - 'ytd_average': Year-to-date average PR%
        - 'lifetime_average': Lifetime average PR%
        - 'trend': Daily PR% for the last `days` days (requires `days`)
        - 'last_n_days_average': Average PR% for the last `days` days (requires `days`)
    days : int, optional
        Number of days for trend or last_n_days_average metrics. Required if `metric_type` is
        'trend' or 'last_n_days_average'.

    Returns
    -------
    str
        Constructed SQL query string.

    Raises
    ------
    ValueError
        If `metric_type` is invalid or if `days` is missing for metrics that require it.

    Example
    -------
    >>> create_sql_query_pr_inverter(
    ...     plant_id="IN.INTE.KIDS",
    ...     inverter_name="INV_1",
    ...     metric_type="monthly_average"
    ... )
    "SELECT plant_name, inverter_name, AVG(edit_pr) AS MONTHLY_AVG_PR_PERCENT FROM solar_inverter_data ..."
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)
    inverter_filter = f"AND inverter_name = '{inverter_name}'" if inverter_name else ""

    if metric_type == "today":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_pr AS PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = '{today}' {inverter_filter};
        """
    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_pr AS PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = '{yesterday}' {inverter_filter};
        """
    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_pr) AS MONTHLY_AVG_PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_pr AS PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND MONTH(date) = {today.month} AND YEAR(date) = {today.year} {inverter_filter}
            ORDER BY edit_pr DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_pr AS PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND MONTH(date) = {today.month} AND YEAR(date) = {today.year} {inverter_filter}
            ORDER BY edit_pr ASC
            LIMIT 1;
        """
    elif metric_type == "ytd_average":
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_pr) AS YTD_AVG_PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND YEAR(date) = {today.year} {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "lifetime_average":
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_pr) AS LIFETIME_AVG_PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_name, inverter_name, date, edit_pr AS PR_PERCENT
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}' {inverter_filter}
            ORDER BY date ASC;
        """
    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_pr) AS AVG_PR_LAST_{days}_DAYS
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}' {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """
    else:
        raise ValueError("Invalid metric_type or missing 'days' for trend/last_n_days_average metrics")

    return sql.strip()



@mcp.tool(
    name="create_sql_query_poa_inverter",
    description=(
        "Generate SQL queries to fetch inverter-level Plane of Array (POA, irradiance) metrics from the `solar_inverter_data` table. "
        "Supports a variety of metric types for flexible reporting and analytics, including: "
        "- 'today': POA for the current date "
        "- 'yesterday': POA for the previous date "
        "- 'monthly_average': Average POA for the current month "
        "- 'highest_day_month': Day with the highest POA in the current month "
        "- 'lowest_day_month': Day with the lowest POA in the current month "
        "- 'daily_average_month': Daily average POA for each day of the current month "
        "- 'trend': Daily POA trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average POA for the last N days (requires 'days' parameter) "
        "- 'compare_last_month': Compare average POA of the current month vs the previous month "
        "The 'plant_id' should be the short name of the plant (e.g., 'IN.INTE.KIDS'). "
        "The 'inverter_name' parameter is optional; if omitted, results are returned for all inverters in the plant. "
        "Use this tool to construct SQL queries for detailed inverter-level POA analysis, reporting, or dashboarding."
    )
)
def create_sql_query_poa_inverter(
    plant_id: str,
    inverter_name: str = None,
    metric_type: str = "today",
    days: int = None
) -> str:
    """
    Generate SQL queries for inverter-level normalized POA (Plane of Array) metrics.

    All POA values are divided by 1000 for unit normalization (e.g., Wh/m² → kWh/m²).

    Parameters
    ----------
    plant_id : str
        Unique identifier or short name of the plant (e.g., 'IN.INTE.KIDS').
    inverter_name : str, optional
        Specific inverter to filter results. If None, returns data for all inverters.
    metric_type : str, default "today"
        Type of POA metric to fetch. Supported values:
        - 'today': POA for the current date
        - 'yesterday': POA for the previous day
        - 'monthly_average': Average POA for the current month
        - 'highest_day_month': Day with highest POA in the current month
        - 'lowest_day_month': Day with lowest POA in the current month
        - 'daily_average_month': Daily average POA for each day of the current month
        - 'trend': Daily POA for the last `days` days (requires `days`)
        - 'last_n_days_average': Average POA for the last `days` days (requires `days`)
        - 'compare_last_month': Compare average POA of current month vs previous month
    days : int, optional
        Number of days for trend or last_n_days_average metrics. Required if `metric_type`
        is 'trend' or 'last_n_days_average'.

    Returns
    -------
    str
        Constructed SQL query string.
    """
    inverter_filter = f"AND inverter_name = '{inverter_name}'" if inverter_name else ""

    if metric_type == "today":
        sql = f"""
            SELECT plant_name, inverter_name, date, (edit_poa / 1000) AS POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE {inverter_filter};
        """

    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_name, inverter_name, date, (edit_poa / 1000) AS POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE - INTERVAL 1 DAY {inverter_filter};
        """

    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_poa / 1000) AS MONTHLY_AVG_POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """

    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, (edit_poa / 1000) AS POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {inverter_filter}
            ORDER BY edit_poa DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, (edit_poa / 1000) AS POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {inverter_filter}
            ORDER BY edit_poa ASC
            LIMIT 1;
        """

    elif metric_type == "daily_average_month":
        sql = f"""
            SELECT plant_name, inverter_name, date, AVG(edit_poa / 1000) AS DAILY_AVG_POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {inverter_filter}
            GROUP BY plant_name, inverter_name, date
            ORDER BY date;
        """

    elif metric_type == "trend":
        if not days:
            raise ValueError("The 'days' parameter is required for the 'trend' metric_type.")
        sql = f"""
            SELECT plant_name, inverter_name, date, (edit_poa / 1000) AS POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date >= CURRENT_DATE - INTERVAL {days} DAY {inverter_filter}
            ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        sql = f"""
            SELECT plant_name, inverter_name, AVG(edit_poa / 1000) AS AVG_POA_LAST_{days}_DAYS
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN CURRENT_DATE - INTERVAL {days} DAY AND CURRENT_DATE {inverter_filter}
            GROUP BY plant_name, inverter_name;
        """

    elif metric_type == "compare_last_month":
        sql = f"""
            SELECT 
                AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                         THEN edit_poa / 1000 END) AS CURRENT_MONTH_POA,
                AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m')
                         THEN edit_poa / 1000 END) AS LAST_MONTH_POA
            FROM solar_inverter_data
            WHERE plant_id = '{plant_id}' {inverter_filter};
        """

    else:
        raise ValueError(f"Unsupported metric_type: {metric_type}")

    return sql.strip()




@mcp.tool(
    name="create_sql_query_avg_wind_speed_turbine",
    description=(
        "Generate SQL queries to fetch wind turbine-level average wind speed metrics from the `wind_turbine_data` table. "
        "Supports a variety of metric types for flexible reporting and analytics, including: "
        "- 'today': Average wind speed for the current date "
        "- 'yesterday': Average wind speed for the previous date "
        "- 'monthly_average': Average wind speed for the current month "
        "- 'highest_day_month': Day with the highest average wind speed in the current month "
        "- 'lowest_day_month': Day with the lowest average wind speed in the current month "
        "- 'trend': Daily average wind speed trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average wind speed for the last N days (requires 'days' parameter) "
        "- 'compare_last_month': Compare average wind speed of the current month vs the previous month "
        "The 'plant_id' should be the short name of the wind plant (e.g., 'IN.INTE.KIDS'). "
        "The 'turbine_name' parameter is optional; if omitted, results are returned for all turbines in the plant. "
        "Use this tool to construct SQL queries for detailed turbine-level wind speed analysis, reporting, or dashboarding."
    )
)
def create_sql_query_avg_wind_speed_turbine(
    plant_id: str,
    turbine_name: str = None,
    metric_type: str = "today",
    days: int = None
) -> str:
    """
    Generate SQL queries for turbine-level average wind speed metrics.

    This function dynamically constructs SQL queries to fetch turbine-level
    average wind speed metrics from the `wind_turbine_data` table. Supported
    metrics include daily values, monthly averages, highest/lowest daily speed,
    trends over a specified number of days, last N days averages, and comparison
    with the previous month.

    Parameters
    ----------
    plant_id : str
        Unique identifier or short name of the plant (e.g., 'IN.INTE.KIDS').
    turbine_name : str, optional
        Specific turbine to filter results. If None, returns data for all turbines.
    metric_type : str, default "today"
        Type of wind speed metric to fetch. Supported values:
        - 'today': Average wind speed for the current date
        - 'yesterday': Average wind speed for the previous day
        - 'monthly_average': Average wind speed for the current month
        - 'highest_day_month': Day with highest average wind speed in the current month
        - 'lowest_day_month': Day with lowest average wind speed in the current month
        - 'trend': Daily average wind speed for the last `days` days (requires `days`)
        - 'last_n_days_average': Average wind speed for the last `days` days (requires `days`)
        - 'compare_last_month': Compare average wind speed of current month vs previous month
    days : int, optional
        Number of days for trend or last_n_days_average metrics. Required if `metric_type`
        is 'trend' or 'last_n_days_average'.

    Returns
    -------
    str
        Constructed SQL query string.

    Raises
    ------
    ValueError
        If `metric_type` is invalid or if `days` is missing for metrics that require it.

    Example
    -------
    >>> create_sql_query_avg_wind_speed_turbine(
    ...     plant_id="IN.INTE.KIDS",
    ...     turbine_name="TURB_1",
    ...     metric_type="monthly_average"
    ... )
    "SELECT plant_name, turbine_name, AVG(edit_avg_wind_speed) AS MONTHLY_AVG_WIND_SPEED FROM wind_turbine_data ..."
    """
    turbine_filter = f"AND turbine_name = '{turbine_name}'" if turbine_name else ""

    if metric_type == "today":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_avg_wind_speed AS AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE {turbine_filter};
        """
    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_avg_wind_speed AS AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE - INTERVAL 1 DAY {turbine_filter};
        """
    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_name, turbine_name, AVG(edit_avg_wind_speed) AS MONTHLY_AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_avg_wind_speed AS AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {turbine_filter}
            ORDER BY edit_avg_wind_speed DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_avg_wind_speed AS AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND DATE_FORMAT(date, '%Y-%m-01') = DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') {turbine_filter}
            ORDER BY edit_avg_wind_speed ASC
            LIMIT 1;
        """
    elif metric_type == "trend":
        if not days:
            raise ValueError("The 'days' parameter is required for the 'trend' metric_type.")
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_avg_wind_speed AS AVG_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date >= CURRENT_DATE - INTERVAL {days} DAY {turbine_filter}
            ORDER BY date;
        """
    elif metric_type == "last_n_days_average" and days:
        sql = f"""
            SELECT plant_name, turbine_name, AVG(edit_avg_wind_speed) AS AVG_WIND_SPEED_LAST_{days}_DAYS
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN CURRENT_DATE - INTERVAL {days} DAY AND CURRENT_DATE {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "compare_last_month":
        sql = f"""
            SELECT 
                AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') THEN edit_avg_wind_speed END) AS CURRENT_MONTH_WIND_SPEED,
                AVG(CASE WHEN DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m') THEN edit_avg_wind_speed END) AS LAST_MONTH_WIND_SPEED
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}' {turbine_filter};
        """
    else:
        raise ValueError(f"Unsupported metric_type: {metric_type}")

    return sql.strip()




@mcp.tool(
    name="create_sql_query_generation_turbine",
    description=(
        "Generate SQL queries to fetch wind turbine-level energy generation metrics from the `wind_turbine_data` table. "
        "Supports a wide range of metric types for flexible reporting and analytics, including: "
        "- 'today': Generation for the current date "
        "- 'yesterday': Generation for the previous date "
        "- 'monthly_total': Total generation for the current month "
        "- 'monthly_average': Average daily generation for the current month "
        "- 'highest_day_month': Day with the highest generation in the current month "
        "- 'lowest_day_month': Day with the lowest generation in the current month "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average generation for the last N days (requires 'days' parameter) "
        "- 'last_n_days_total': Total generation for the last N days (requires 'days' parameter) "
        "- 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today) "
        "- 'lifetime_total': Total generation across all available records for the turbine "
        "The 'plant_id' should be the short name of the wind plant (e.g., 'IN.INTE.KIDS'). "
        "The 'turbine_name' parameter is optional; if omitted, results are returned for all turbines in the plant. "
        "Use this tool to construct SQL queries for detailed turbine-level generation analysis, reporting, or dashboarding."
    )
)
def create_sql_query_generation_turbine(
    plant_id: str,
    turbine_name: str = None,
    metric_type: str = "today",
    days: int = None
) -> str:
    """
    Generate SQL queries for turbine-level energy generation metrics.

    This function dynamically constructs SQL queries to fetch turbine-level
    generation metrics from the `wind_turbine_data` table. Supported metrics
    include daily generation, monthly totals/averages, highest/lowest daily
    generation, trends over a specified number of days, last N days averages/totals,
    YTD total, and lifetime total generation.

    Parameters
    ----------
    plant_id : str
        Unique identifier or short name of the plant (e.g., 'IN.INTE.KIDS').
    turbine_name : str, optional
        Specific turbine to filter results. If None, returns data for all turbines.
    metric_type : str, default "today"
        Type of generation metric to fetch. Supported values:
        - 'today': Generation for the current day
        - 'yesterday': Generation for the previous day
        - 'monthly_total': Sum of generation for the current month
        - 'monthly_average': Average daily generation for the current month
        - 'highest_day_month': Day with highest generation in the current month
        - 'lowest_day_month': Day with lowest generation in the current month
        - 'trend': Daily generation over the last `days` days (requires `days`)
        - 'last_n_days_average': Average generation over the last `days` days (requires `days`)
        - 'last_n_days_total': Total generation over the last `days` days (requires `days`)
        - 'ytd_total': Total generation for the current year
        - 'lifetime_total': Total generation for the turbine across all available data
    days : int, optional
        Number of days for trend, last_n_days_average, or last_n_days_total metrics.

    Returns
    -------
    str
        Constructed SQL query string.

    Raises
    ------
    ValueError
        If `metric_type` is invalid or if `days` is missing for metrics that require it.

    Example
    -------
    >>> create_sql_query_generation_turbine(
    ...     plant_id="IN.INTE.KIDS",
    ...     turbine_name="TURB_1",
    ...     metric_type="monthly_total"
    ... )
    "SELECT plant_name, turbine_name, SUM(edit_generation) AS MONTHLY_GENERATION ..."
    """
    turbine_filter = f"AND turbine_name = '{turbine_name}'" if turbine_name else ""
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_generation AS DAILY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE {turbine_filter};
        """
    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_generation AS DAILY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date = CURRENT_DATE - INTERVAL 1 DAY {turbine_filter};
        """
    elif metric_type == "monthly_total":
        sql = f"""
            SELECT plant_name, turbine_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}' {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "monthly_average":
        sql = f"""
            SELECT plant_name, turbine_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}' {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_generation AS DAILY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year} {turbine_filter}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_generation AS DAILY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year} {turbine_filter}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """
    elif metric_type == "trend":
        if not days:
            raise ValueError("The 'days' parameter is required for the 'trend' metric_type.")
        sql = f"""
            SELECT plant_name, turbine_name, date, edit_generation AS DAILY_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date >= CURRENT_DATE - INTERVAL {days} DAY {turbine_filter}
            ORDER BY date;
        """
    elif metric_type == "last_n_days_average" and days:
        sql = f"""
            SELECT plant_name, turbine_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN CURRENT_DATE - INTERVAL {days} DAY AND CURRENT_DATE {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "last_n_days_total" and days:
        sql = f"""
            SELECT plant_name, turbine_name, SUM(edit_generation) AS TOTAL_GENERATION_LAST_{days}_DAYS
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND date BETWEEN CURRENT_DATE - INTERVAL {days} DAY AND CURRENT_DATE {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "ytd_total":
        sql = f"""
            SELECT plant_name, turbine_name, SUM(edit_generation) AS YTD_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}'
              AND EXTRACT(YEAR FROM date) = {today.year} {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    elif metric_type == "lifetime_total":
        sql = f"""
            SELECT plant_name, turbine_name, SUM(edit_generation) AS LIFETIME_GENERATION
            FROM wind_turbine_data
            WHERE plant_id = '{plant_id}' {turbine_filter}
            GROUP BY plant_name, turbine_name;
        """
    else:
        raise ValueError(f"Unsupported metric_type: {metric_type}")

    return sql.strip()






# @mcp.tool(
#     name="get_solar_plot",  # programmatic identifier
#     title="Daily Solar Generation Plot",  # human-readable title
#     description="This PNG plot shows the daily solar energy generation for the plant. "
#                 "The LLM can analyze trends, anomalies, and performance metrics from this image."
# )
# def get_solar_plot():
#     """
#     Returns the latest daily solar generation plot as an image resource for LLM analysis.
#     """
#     return Image.from_file(r"D:/DGR-Generation/export_plots/generation_1757499555.png")


# =========================
# Main Entry Point
# =========================

if __name__ == "__main__":
    logger.info("Solar MCP server starting")
    try:
        # Default transport will be stdio. In production you might use grpc/http when supported.
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as exc:
        logger.exception("Unhandled exception while running MCP: %s", exc)